\section{Introduction}
\label{sec:intro}
Electron microscopy (EM) imaging, with its nanometer-scale resolution, has become a core tool for deciphering the ultrastructure of tissues and organelles in neuroscience and cell biology. Advances in imaging instruments such as focused ion beam scanning electron microscopes (FIB-SEMs) have enabled researchers to rapidly acquire large-scale, high-resolution 3D EM data. This has enabled researchers to uncover subcellular morphological features and uncover underlying mechanisms. Gaining biological insights from these data requires accurate, automated segmentation of cells and organelles. However, large-scale, high-resolution 3D EM data presents significant challenges for image segmentation. The sheer size of these datasets makes traditional or semi-automated manual annotation methods impractical and time-consuming. Furthermore, the objects to be segmented in EM images (such as axons, cells, mitochondria, and synapses) often have complex morphologies, fuzzy boundaries, and are widely distributed across the spatial domain, posing a significant challenge to accurate localization and boundary delineation.

Convolutional neural network-based architectures have been widely used in 3D biological image segmentation research. The classic U-Net and its improved versions have achieved significant progress in cell and organelle segmentation\cite{ronneberger2015u,cao2020denseunet,quan2021fusionnet}. With the increasing demand for modeling long-range dependencies, Transformers have been introduced into segmentation networks\cite{pan2023adaptive,luo2024electron,zhang2025swincell}, demonstrating enhanced contextual awareness in medical and biological images. However, these methods rely on extensive manual annotation and often face challenges with computational overhead and lack of spatial consistency in large-scale, high-resolution 3D scenes. Semi-supervised and self-supervised learning, on the other hand, have been widely explored to alleviate the annotation cost issue. Typical approaches include pseudo-label learning based on consistency regularization\cite{takaya2021sequential}, contrastive learning, and self-supervised pre-training\cite{conrad2021cem500k}. These methods have made progress in reducing annotation requirements but remain deficient in modeling the spatial continuity of complex 3D structures.

The recently proposed general segmentation model SAM\cite{kirillov2023segment} has demonstrated impressive zero-shot and few-shot capabilities and has been initially applied to biomedical image segmentation\cite{ma2024segment,archit2025segment,shah2025sam4em}. However, these methods primarily segment independent 2D slices, resulting in insufficient spatial continuity across slices and difficulty ensuring structural integrity and coherence. SAM's successor, SAM2\cite{ravi2024sam}, and some other SAM variants, such as SAM4EM\cite{shah2025sam4em}, incorporate memory mechanisms into the model, providing a certain degree of spatial continuity. However, these memories still rely on the segmentation results of their own historical frames and cannot explicitly capture the 3D representation of the segmented objects.

Against this backdrop, this paper proposes a novel segmentation framework for high-resolution 3D EM images. Our main contributions can be summarized as follows:
First, we propose a 3D segmentation model based on SAM2. By introducing the Signed Distance Function (SDF) as a memory mechanism, we effectively improve spatial consistency across slices. Second, we design a joint training method combining U-Net SDF with SAM2, leveraging the few-shot capabilities of the large model and enabling rapid adaptation to new datasets with minimal annotation. Finally, we develop a comprehensive toolchain, covering the entire process from interactive annotation to fully automated segmentation, providing an efficient and practical solution for studying large-scale 3D EM data.

We conduct extensive experiments on several challenging large-scale 3D EM datasets, including annotated data of mitochondria and nuclei. Our experimental results show that our method achieves comparable or better segmentation performance than state-of-the-art semi-supervised and even fully-supervised methods while using less labeled data than most semi-supervised methods.