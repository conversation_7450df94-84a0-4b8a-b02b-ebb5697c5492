\section{Method}
\label{sec:method}

Our goal is to extend SAM2 to fully automatic 3D electron microscopy image segmentation while ensuring spatial consistency and annotation efficient training. The overall approach consists of two main components: (1) spatially consistent prompt-free model based on a precomputed memory representation of the 3D Signed Distance Field (SDF); and (2) combining SAM2's few-shot capabilities with pseudo-label learning to achieve low-annotation-cost semi-supervised training of Spatial-SAM.

\subsection{SAM 2 Architecture}
SAM2 extends the Segment Anything paradigm from static images to videos by introducing a unified framework for promptable visual segmentation across time. Its overall design inherits the image segmentation capability of SAM while crucially augmenting it with a memory mechanism that supports temporal consistency. The image encoder processes frames in a streaming manner and generates multi-scale embeddings using a Hiera backbone pre-trained with masked autoencoding, providing a strong visual representation. Prompts such as points, bounding boxes, or masks are encoded in the same way as in SAM, and the mask decoder adopts the “two-way” Transformer structure to jointly refine prompt embeddings and frame features, producing multiple candidate masks to resolve potential ambiguities. 

To handle temporal dynamics, SAM2 integrates a memory system that accumulates object-related information over time. Specifically, the memory encoder fuses the predicted mask with the corresponding frame embedding, producing memory features that are stored in a memory bank. These memory entries, together with lightweight object pointer tokens distilled from the decoder, are accessed through memory attention, enabling the current frame to be conditioned on both past predictions and spatial context from previous frames. This tightly coupled architecture allows SAM2 to propagate segmentation consistently across video frames, maintaining object identity even under motion or occlusion, and forms the foundation for adapting SAM to three-dimensional biomedical image volumes in our work.



\subsection{空间一致的 Prompt-Free AUTO-SAM}
Our goal is to extend SAM2 to the task of fully automatic 3D volume segmentation while ensuring spatial consistency of the segmentation results. The easiest approach is to slice the 3D volume along a specific axis and propagate this information frame by frame through SAM2's memory mechanism. Specifically, let the input volume be $V \in \mathbb{R}^{D \times H \times W}$. Slicing along the axis yields $\{I_1, I_2, \dots, I_D\}$. SAM2 can encode the features of $\{I_{t-k}, \dots, I_{t-1}\}$ and the mask logit as memory when segmenting $I_t$. However, this one-way propagation has certain limitations. First, it is directional-dependent: SAM2's memory only contains historical frames in the propagation direction and cannot utilize information from future frames, resulting in inconsistent segmentation quality across different propagation directions. Second, it can lead to error accumulation: if a prediction error occurs in a frame, the erroneous result is stored in the memory bank and amplified in subsequent propagations. The third is the sensitivity to slice selection, that is, the selection of propagation axis and conditional frame will significantly affect the final segmentation results.

To address these issues, we propose a pre-computed 3D SDF memory mechanism. Using a lightweight 3D segmentation network (3D-UNet), we compute a coarse signed distance field (SDF) for the downsampled volume. The slice of 3D SDF, along with image features, is fed into the SAM2 memory encoder to generate a 3D memory with no orientation dependency. This allows the memory to contain both front- and back-end information of the slice, avoiding orientation dependency. Furthermore, single-frame errors are not written to the memory bank since the SDF is pre-computed out of SAM2, reducing error accumulation. Furthermore, the pre-computed memory acts as a prompt for segmenting objects enables prompt-free automatic segmentation.

\subsection{3D SDF Memory}
In the original SAM2, the memory encoder takes mask logit and frame embedding as input, thus preserving historical segmentation results to a certain extent. However, logit is inherently a probabilistic representation, which makes it difficult to provide the model with richer geometric constraints. To address this issue, we propose using the 3D Signed Distance Field (SDF) as the core representation of the memory to incorporate more complete 3D semantic information.

For any point $\mathbf{x} \in \mathbb{R}^3$, its SDF is defined as
\begin{equation}
    \text{SDF}(\mathbf{x}) = 
    \begin{cases}
    + \min_{\mathbf{y} \in \partial \Omega} \|\mathbf{x}-\mathbf{y}\|, & \mathbf{x} \in \Omega, \\[4pt]
    - \min_{\mathbf{y} \in \partial \Omega} \|\mathbf{x}-\mathbf{y}\|, & \mathbf{x} \notin \Omega,
    \end{cases}
\end{equation}
Where $\Omega$ represents the object area, $\partial \Omega$ is its boundary, and we set positive inside and negative outside to align with the logit distribution. Compared to the logit map, the SDF offers two significant advantages: first, as an implicit representation of a 3D object, SDF provides the model with relatively complete semantic information of the segmentation target in 3D space; second, even with limited memory bank length, the distance information contained in the SDF provides the model with a wider range of global geometric awareness, thereby improving spatial consistency between slices.

We first use 3D-UNet to predict an SDF grid $\hat{S} \in \mathbb{R}^{D' \times H' \times W'}$ on a low-resolution volume and upsample it to the original resolution. The input volume $V$ and the upsampled $\hat{S}$ are then sliced ​​along the same axis. The image slice $I_t$ is passed through the SAM2 image encoder to obtain a feature representation $\phi(I_t)$. The corresponding SDF slice, along with $\phi(I_t)$, is then fed into the memory encoder to generate the slice's memory representation $M_t$ and stored in the memory bank. When segmenting the target slice $I_t$, we retrieve the $K$ frames $\{M_{t-K}, \dots, M_{t-1}, M_{t+1}, \dots, M_{t+K}\}$ from the memory bank, perform memory attention on them along with the current image feature $\phi(I_t)$, and predict the segmentation result through the mask decoder. It is important to emphasize that to prevent the coarse SDF prediction from directly interfering with the segmentation of the current slice, we do not use the memory corresponding to $I_t$, but only utilize the SDF memory of its neighboring slices.
% TODO: neighboring slices 表达是否正确？

In addition to semantic segmentation, the SDF predicted by 3D-UNet can be further used for instance segmentation. We generate initial seed points based on the SDF and use the watershed algorithm with the final segmentation mask to achieve instance-based segmentation of the object. This allows SDF memory to not only improve spatial consistency but also provides a natural path for scaling from semantic to instance-level tasks.


\subsection{Training}
To significantly reduce the cost of manual annotation for large-scale volume electron microscopy datasets, we propose a training framework that combines Fewshot with semi-supervised learning, as shown in Figure \ref{fig:training}. This framework leverages the interactive segmentation capabilities of SAM2 to generate high-quality pseudo-labels and gradually improves overall segmentation performance by alternately optimizing the UNet module and the SAM2 module.

Assume that the large-scale dataset is $\mathcal{D}{\text{all}}={I_i}_{i=1}^M$. We first randomly sample several volumetric slices from $\mathcal{D}{\text{all}}$ as the training set $\mathcal{D}={I_i}_{i=1}^N$. Subsequently, we further select $m$ 2D slices ${I_1, \dots, I_m}$ from $\mathcal{D}$, perform interactive segmentation using SAM2, and manually refine them to obtain high-quality annotations ${Y_1, \dots, Y_m}$. These annotations are input into SAM2 as conditional frames, resulting in the initial pseudo-label set $\tilde{Y}={\tilde{Y}i}_{i=1}^N$ for the entire training set. Unlike traditional pseudo-label learning methods (such as 4S\cite{takaya2021sequential}), which require training the model from scratch to generate pseudo-labels, we directly utilize SAM2's Fewshot capability to generate pseudo-labels. This allows us to generate higher-quality initial pseudo-labels with minimal annotated data, making training easier to converge and reducing the risk of model overfitting.

Inspired by CPS\cite{chen2021semi}, we designed an alternating training mechanism for UNet and SAM2 modules. Unlike the symmetric two-branch consistency constraint in CPS, our approach decouples the two modules, each of which takes on distinct tasks:

\begin{enumerate}
    \item \textbf{UNet Training:} Convert the pseudo-labels $\tilde{Y}$ into a 3D SDF $S$, which is used as a supervisory signal to train the UNet module, resulting in the predicted SDF $\hat{S}$. This step enables the UNet to learn the geometric representation about segmenting objects from the image.
    \item \textbf{SAM2 Training:} Use the UNet predicted $\hat{S}$ to generate new pseudo-labels $\tilde{Y}'$, and combine them with the original small number of annotations $\{Y_j\}_{j=1}^m$ to train the SAM2 module. 
    \item \textbf{Iterative Optimization:} After training, SAM2 can be re-inferred on the entire dataset to generate higher-quality pseudo-labels $\tilde{Y}^{(t+1)}$ for use in the next iteration. 
\end{enumerate}

In this way, the UNet module continuously learns more accurate 3D geometry, while the SAM2 module improves global segmentation performance driven by high-quality pseudo-labels. The two complement each other. It is worth noting that SAM2 uses a memory mechanism to fuse the UNet's 3D geometric representation with its own high-resolution feature representation. Therefore, during training, we sample annotated slices and their neighbors with probability $p$ and any slice with probability $1-p$, ensuring full utilization of Ground Truth and preventing the accumulation and propagation of pseudo-label errors. 
% TODO: 且SAM2在分割切片时仅使用该切片前后6帧的SDF memory，不会使用当前切片的SDF memory，进一步地为了防止SAM过度依赖UNet生成的SDF信息。

\textbf{(1) UNet module.}  
UNet predicts a 3D SDF and we use the following loss function:
\begin{equation}
    \mathcal{L}_{\text{UNet}} = \mathcal{L}_{\text{MSE}}(\hat{S}, S) + \lambda \mathcal{L}_{\text{Eikonal}},
\end{equation}
Where $\mathcal{L}_{\text{MSE}}$ is used to supervise the numerical consistency of the predicted SDF and the true SDF, while $\mathcal{L}_{\text{Eikonal}}$ constrains the prediction field to satisfy $\|\nabla \hat{S}(\mathbf{x})\|\approx 1$, ensuring the geometric rationality of the SDF. Specifically defined as:
\begin{equation}
    \mathcal{L}_{\text{Eikonal}} = \frac{1}{|\Omega|} \sum_{\mathbf{x}\in \Omega} \left(\|\nabla \hat{S}(\mathbf{x})\| - 1 \right)^2.
\end{equation}

\textbf{(2) SAM2 module.}  
For SAM2, we retain its original segmentation loss design but remove the classification loss:
\begin{equation}
    \mathcal{L}_{\text{SAM2}} = \alpha \mathcal{L}_{\text{Dice}} + \beta \mathcal{L}_{\text{IoU}} + \gamma \mathcal{L}_{\text{Focal}},
\end{equation}
Where $\mathcal{L}_{\text{Dice}}$ and $\mathcal{L}_{\text{IoU}}$ ensures the overlap between the predicted and true masks in the global space, and $\mathcal{L}_{\text{Focal}}$ solves the foreground/background class imbalance problem by reducing the weight of easily classified samples.

Through the above design, UNet is responsible for learning stable 3D geometric representation, while SAM2 learns fine mask segmentation based on this. The two complement each other and ultimately achieve high-quality fully automatic 3D electron microscopy segmentation.


% \subsection{Toolset Implementation}
% We provide a full-process toolset based on the Napari plugin, covering:
% \begin{itemize}
%     \item \textbf{Interactive Segmentation}: Supports fast annotation of 2D or 3D slices using points and box hints;
%     \item \textbf{Model Training}: Trains UNet and Spatial-SAM models based on a small number of annotated slices to adapt to new datasets;
%     \item \textbf{Fully Automatic Segmentation}: Calls Spatial-SAM to perform semantic/instance segmentation on 3D volumes;
%     \item \textbf{Annotation Correction and Retraining}: Supports interactive correction of automatic results and iterative model optimization;
%     \item \textbf{Hardware Adaptation}: Allows users to set resolution and memory usage based on device conditions.
% \end{itemize}