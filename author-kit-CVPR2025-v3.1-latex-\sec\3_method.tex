\section{Method}

\subsection{SAM 2 Architecture}
SAM 2 is developed as a unified foundation model for promptable visual segmentation in both images and videos, generalizing the Segment Anything (SA) task to the temporal domain. While sharing core principles with SAM, SAM 2's capacity for video processing is crucially enabled by its enhanced memory mechanism.

The SAM 2 architecture comprises:
Image Encoder: This component processes video frames in a streaming fashion, providing unconditioned feature embeddings for each frame. It uses an MAE pre-trained Hiera image encoder, which supports multiscale features for decoding.Prompt Encoder: Identical to SAM's, it encodes prompts such as clicks (positive/negative), bounding boxes, or masks to define the object's extent in a frame.Mask Decoder: Following SAM's design, it uses "two-way" Transformer blocks to update prompt and frame embeddings. It predicts multiple masks for ambiguous prompts to ensure valid outputs, a feature extended to video frames where ambiguity can persist over time. An occlusion prediction head is also included to forecast if the object of interest is visible in the current frame.

The memory mechanism is central to SAM 2's ability to track objects and maintain temporal coherence in videos:
Memory Attention: This module is critical for conditioning the current frame's features on information from past frames and previous predictions, as well as any new prompts. It consists of Transformer blocks that perform self-attention and then cross-attention to a "memory bank".Memory Bank: This acts as a repository for historical object information. It stores spatial feature maps from past frames and information from previously prompted frames.Memory Encoder: This component is responsible for generating the memory that populates the memory bank. It fuses the current output mask (derived from the mask decoder's prediction) with the unconditioned frame embedding from the image encoder. Specifically, the mask token from the mask decoder's output is used as an "object pointer token" and placed into the memory bank.Object Pointers: These are lightweight vectors, derived from the mask decoder's output tokens, that provide high-level semantic information about the object being segmented. They are cross-attended to by the memory attention module, enhancing tracking performance.

This integrated memory architecture enables SAM 2 to effectively leverage temporal context, allowing for real-time video processing and coherent object tracking across frames, even in dynamic or occluded scenarios.


\subsection{3D Segmentation}
Our goal is to apply SAM to fully automatic semantic segmentation while providing the model with richer 3D information to ensure spatial consistency and continuity of the segmentation results. As described in Section xx, SAM2's memory mechanism can obtain segmentation information from previous frames—as well as conditional frames—to maintain a certain degree of temporal consistency. Therefore, a common approach is to decompose the 3D image into a series of 2D slices along a certain axis and then use SAM2 to segment each slice along that axis, either front-to-back or back-to-front. As shown in [], SAM2's memory mechanism can be used to maintain a certain degree of spatial consistency by leveraging the features and segmentation information of a certain number of slices in the reverse direction of propagation when segmenting the 2D slices. However, SAM2's memory only provides features and segmentation information for a limited number of slices in the reverse direction of propagation and cannot obtain features and segmentation information for unsegmented slices in the forward direction of propagation. This directional nature significantly affects the choice of propagation direction on segmentation quality. Furthermore, the choice of the slice decomposition axis and the selection of the conditional frames also affect segmentation quality []. If segmentation errors occur during propagation, the erroneous results will be recorded in the memory bank, affecting subsequent segmentation and causing object tracking failure.

To avoid these issues, we propose a pre-computed 3D memory mechanism that ensures spatial consistency. The core idea is to use a lightweight 3D segmentation model to pre-compute a coarse distance field for the downsampled volume. This is then combined with image features via a memory encoder to generate memory. The SAM2 mask decoder then extracts information from this pre-computed memory to segment the original resolution 3D image. Because the memory is pre-computed, the memory bank can be expanded to include bidirectional slices before and after the current segmentation slice. This not only provides more spatial information but also remains unaffected by the propagation direction. Furthermore, because the 2D slice predictions are not used as memory, a single slice prediction error does not affect subsequent slice predictions, resolving the issue of target tracking failure. Furthermore, by leveraging SAM2's ability to perform segmentation based on memory and input image pairs without prompting when the memory bank is not empty, we can achieve fully automatic segmentation using a prompt-free approach.

\subsection{SDF Memory}
SAM2 uses a memory encoder to fuse the output masked logit with the frame embedding to compute memory. Thanks to the 3D segmentation model we used to precompute memory, we can use a signed distance field (SDF) in 3D space to encode memory instead of a masked logit. A signed distance field (SDF) is a representation of geometry that stores the closest distance to the surface of each point in space, with the sign of the distance indicating whether the point is inside or outside the geometry. We want it to be similar in form to the logit, so we use negative values ​​outside and positive values ​​inside. The formula is as follows.

Where... Since we use a 3D-UNet to predict the SDF grid, the maximum distance in the SDF should not exceed the receptive field of the convolution, otherwise it will fail to predict correctly. Furthermore, setting a threshold can also reduce training complexity to some extent. Compared to the logit, the SDF provides clearer geometric information. Even within a very limited memory bank, the model can indirectly obtain a wider range of spatial information through distance information. [How to make it easier to understand? ] Specifically, we upsample the three-dimensional SDF grid predicted by UNet to the original image resolution, and slice it along the same axis as the original image. The image slices are sent to the image encoder of SAM2 to obtain feature maps, which are sent to the memory encoder together with the SDF slices to obtain memory and store them in the memory bank. When performing segmentation, we select the memory of the previous 6 frames and the next 6 frames of the current segmentation slice and the feature map of the current slice for memory attention and mask decoder to obtain the segmentation result. (In order to prevent the relatively coarse SDF of UNet on the current slice from affecting the fine segmentation of SAM, only the memory of the previous and next frames is used, and the memory of the current frame is not used.) Finally, the SDF grid predicted by 3D-UNet can also help generate seed points so that the watershed algorithm can be used to convert semantic segmentation into instance segmentation. [It should be emphasized that even if it is only used as an encoding memory, the coarse SDF can also do the job]


\subsection{Few-shot Training of 3D-UNet}
Because the UNet module in our pipeline is relatively independent, accepting 3D images as input and outputting the SDF corresponding to the segmented objects, it is plug-and-play: for any segmentation task, simply replace the UNet model that predicts the corresponding object SDF. While fully supervised training of this independent UNet module is possible, high-resolution 3D EM images often lack sufficient annotations for fully supervised training of 3D neural networks. Therefore, we leverage the fewshot capability of SAM2 to train the module with minimal annotated data. Specifically, we select a certain number of 3D images (of dimensions D = W = H) from the entire dataset, which will serve as the training set for the UNet. We decompose these 3D images into 2D slices, annotate the slices at regular intervals, and use these labeled slices as cue frames to predict these volumes using SAM2 (or SAM2 fine-tuned on a general EM dataset) [it is important to note that SAM2 is different from the SAM2 used in our pipeline]. The predicted results serve as ground truth along with the original images to train the UNet. While using SAM2 predictions as ground truth inevitably results in some segmentation errors, since the purpose of training UNet is solely to provide SAM2 with the SDF required for memory encoding, experiments have shown that even a coarse SDF is sufficient. To prevent the choice of slice decomposition axis from affecting segmentation quality, we use orthogonal segmentation [], which decomposes and predicts along all three axes and averages the resulting logits. This approach often yields better results than predicting along any direction individually.


\subsection{Training}
To adapt our SAM to the modified memory mechanism, correctly extract information from the memory and predict high-quality segmentations, while also improving its segmentation performance on a variety of volume electron microscopy data, we developed a fine-tuning scheme for mSAM. We fine-tuned mSAM on a variety of datasets segmenting different organelle types. Since a single Unified Network (UNet) module can only predict a specific segmentation task, we independently trained a UNet module on each dataset and precomputed its results. The loss function used for training the UNet is a weighted sum of the MSE loss and the Eikonal loss, as follows:

The other modules of mSAM are uniformly fine-tuned across all datasets. To enable mSAM to better learn the additional geometric information provided by using the SDF encoded in the memory, we employed a multi-task learning scheme. Specifically, we extended mSAM's mask decoder (taking a 3D image slice as input and the precomputed SDF output by the UNet) to simultaneously predict the mask logit and SDF for the current frame based on information from the previous and next frame's memory. Since predicting SDF is a more difficult task than predicting segmentation mask, adding this task to training can impose stronger constraints on the model to better utilize the SDF information in memory. For the predicted SDF, we use MSE Loss as the loss function. For the predicted mask logit, we use the loss function used by SAM2, which is the weighted sum of Focal Loss, Dice Loss, IoU Loss and classification loss. The formula is as follows: