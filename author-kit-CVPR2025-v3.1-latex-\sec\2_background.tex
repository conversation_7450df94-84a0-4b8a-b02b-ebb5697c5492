\section{Related Works}

\subsection{Electron Microscope Image Segmentation Methods}

Traditional electron microscopy image segmentation relies primarily on techniques such as threshold segmentation, eigenvector analysis\cite{FRANGAKIS2002105}, and hierarchical region merging using the watershed algorithm\cite{liu2012watershed}. However, these traditional methods often struggle to scale with complex structures. In recent years, deep learning has become the mainstream approach in the segmentation field. End-to-end segmentation models based on convolutional neural networks (CNNs), such as FCN\cite{long2015fully} and U-Net\cite{ronneberger2015u}, have been widely used for cell and organelle segmentation tasks\cite{ronneberger2015u,quan2021fusionnet}. U-Net, through its symmetrical encoder-decoder architecture and skip connections, integrates multi-scale contextual information, significantly improving segmentation accuracy. For 3D EM data, researchers have developed 3D convolutional networks such as 3D U-Net\cite{cciccek20163d} and V-Net\cite{milletari2016v} and applied them to EM data segmentation. 3D networks can learn volumetric features directly at the voxel level, but the increased dimensionality increases computational complexity and limits input resolution. Additionally, methods such as hybrid 2D-3D networks and flood-filling networks have emerged. With the increasing demand for long-range contextual dependencies, networks based on self-attention mechanisms, such as the Transformer, have also been introduced for medical and biological image segmentation\cite{pan2023adaptive,luo2024electron,zhang2025swincell}. They demonstrate excellent performance in capturing long-range pixel relationships and global context. In summary, deep learning models improve segmentation accuracy by automatically extracting features, but they usually require a large amount of labeled data and expensive computing resources, and the segmentation of high-resolution volume data remains challenging.

Semi-supervised learning methods have been widely explored to reduce the cost of annotation. In semi-supervised learning, strategies such as pseudo-label propagation and consistency regularization are often used to utilize unlabeled data. For example, Takaya et al.\cite{takaya2021sequential} proposed the "4S" method, which iteratively generates pseudo-labels to expand the annotation set by leveraging the correlation between adjacent slices in the volume; Wolny et al.\cite{wolny2022sparse} treated unlabeled areas as background for mitochondrial segmentation, and introduced embedding consistency loss and push-pull loss on two data augmentation paths to enhance the distinguishability of different instances in the embedding space. Mai et al.\cite{mai2023dualrel} implemented an end-to-end "double reliable" network, including reliable pixel aggregation and prototype selection modules, to achieve semi-supervised learning of mitochondrial segmentation. In terms of self-supervised pre-training, Conrad and Narayan\cite{conrad2021cem500k} used contrastive learning to pre-train the network on a large-scale unlabeled cell EM image set, and then completed the specific segmentation task through fine-tuning. These methods have alleviated the labeling requirements to a certain extent, but since they are all based on two-dimensional slices for segmentation, they are still insufficient in modeling the spatial continuity of complex three-dimensional structures, and are prone to problems such as breaks between slices or omission of subtle structures.

\subsection{SAM/SAM2 and Its Applications in Biomedical Imaging}

The Segment Anything Model (SAM)\cite{kirillov2023segment} is a general-purpose segmentation base model proposed by Meta AI in 2023. Its core consists of a Vision Transformer-based image encoder and a hinted mask decoder. SAM eliminates the need for custom network architectures for each task and can rapidly generate object segmentations using "hints" such as points, boxes, or text. It demonstrates strong zero-shot and few-shot adaptability. In the field of biomedical imaging, several works have attempted to adapt SAM to specific tasks. For example, Ma et al.\cite{ma2024segment} proposed MedSAM for medical image segmentation. By fine-tuning SAM on multimodal medical datasets, they significantly improved segmentation performance. In microscopy image analysis, Archit et al.\cite{archit2025segment} proposed the μSAM framework, specifically fine-tuning SAM for optical and electron microscopy data. Results showed significant improvements in segmentation quality under various imaging conditions. These studies typically rely on two-dimensional strategies: they segment volumetric data as a series of independent image slices, making it difficult to directly model the spatial correlation between slices.

The recently released SAM2 model\cite{ravi2024sam} builds on this foundation by adopting a Transformer architecture with a streaming memory mechanism and incorporating large-scale video segmentation data. This more efficient design achieves higher accuracy and a 6x speedup on image segmentation tasks compared to SAM. Shah et al.\cite{shah2025sam4em} implemented 3D memory using momentum updates and fine-tuned the model in conjunction with LoRA. While these methods can, to a certain extent, reference the segmentation results and image features of previous slices when segmenting slices, imperfections in the segmentation results of a single historical slice can affect the model's overall understanding of the object, leading to decreased segmentation accuracy during propagation. Our approach addresses this issue by incorporating the 3D representation of the object into the memory encoding.