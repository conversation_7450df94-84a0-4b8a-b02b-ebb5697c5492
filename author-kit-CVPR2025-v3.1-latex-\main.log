This is XeTeX, Version 3.141592653-2.6-0.999996 (TeX Live 2024) (preloaded format=xelatex 2024.4.17)  19 SEP 2025 21:21
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**main
(./main.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(f:/dev/texlive/2024/texmf-dist/tex/latex/base/article.cls
Document Class: article 2023/05/17 v1.4n Standard LaTeX document class
(f:/dev/texlive/2024/texmf-dist/tex/latex/base/size10.clo
File: size10.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
\c@part=\count184
\c@section=\count185
\c@subsection=\count186
\c@subsubsection=\count187
\c@paragraph=\count188
\c@subparagraph=\count189
\c@figure=\count190
\c@table=\count191
\abovecaptionskip=\skip48
\belowcaptionskip=\skip49
\bibindent=\dimen140
) (./cvpr.sty
Package: cvpr 2025 LaTeX class for IEEE CVPR
 (f:/dev/texlive/2024/texmf-dist/tex/latex/psnfss/times.sty
Package: times 2020/03/25 PSNFSS-v9.3 (SPQR) 
) (f:/dev/texlive/2024/texmf-dist/tex/latex/tools/xspace.sty
Package: xspace 2014/10/28 v1.13 Space after command names (DPC,MH)
) (f:/dev/texlive/2024/texmf-dist/tex/latex/xcolor/xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)
 (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics-cfg/color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 274.
 (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
) (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics/mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
) (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics/dvipsnam.def
File: dvipsnam.def 2016/06/17 v3.0m Driver-dependent file (DPC,SPQR)
) (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics/keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
) (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (f:/dev/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 107.
)
\Gin@req@height=\dimen141
\Gin@req@width=\dimen142
) (f:/dev/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip50

For additional information on amsmath, use the `?' option.
(f:/dev/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (f:/dev/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen143
)) (f:/dev/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen144
) (f:/dev/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count192
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count193
\leftroot@=\count194
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count195
\DOTSCASE@=\count196
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box51
\strutbox@=\box52
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen145
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count197
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count198
\dotsspace@=\muskip16
\c@parentequation=\count199
\dspbrk@lvl=\count266
\tag@help=\toks19
\row@=\count267
\column@=\count268
\maxfields@=\count269
\andhelp@=\toks20
\eqnshift@=\dimen146
\alignsep@=\dimen147
\tagshift@=\dimen148
\tagwidth@=\dimen149
\totwidth@=\dimen150
\lineht@=\dimen151
\@envbody=\toks21
\multlinegap=\skip51
\multlinetaggap=\skip52
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (f:/dev/texlive/2024/texmf-dist/tex/latex/amsfonts/amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols
 (f:/dev/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
)) (f:/dev/texlive/2024/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen152
\lightrulewidth=\dimen153
\cmidrulewidth=\dimen154
\belowrulesep=\dimen155
\belowbottomsep=\dimen156
\aboverulesep=\dimen157
\abovetopsep=\dimen158
\cmidrulesep=\dimen159
\cmidrulekern=\dimen160
\defaultaddspace=\dimen161
\@cmidla=\count270
\@cmidlb=\count271
\@aboverulesep=\dimen162
\@belowrulesep=\dimen163
\@thisruleclass=\count272
\@lastruleclass=\count273
\@thisrulewidth=\dimen164
) (f:/dev/texlive/2024/texmf-dist/tex/latex/natbib/natbib.sty
Package: natbib 2010/09/13 8.31b (PWD, AO)
\bibhang=\skip53
\bibsep=\skip54
LaTeX Info: Redefining \cite on input line 694.
\c@NAT@ctr=\count274
) (f:/dev/texlive/2024/texmf-dist/tex/latex/silence/silence.sty
Package: silence 2012/07/02 v1.5b Selective filtering of warnings and error messages
\sl@Save=\count275
\sl@StateNumber=\count276
\sl@MessageCount=\count277
\sl@Casualties=\count278
\sl@Filter=\toks23
\sl@Message=\toks24
\sl@UnexpandedMessage=\toks25
\sl@Mess@ge=\toks26
\sl@WarningCount=\count279
\sl@WarningNumber=\count280
\sl@WarningCasualties=\count281
\sl@TempBOW=\toks27
\sl@BankOfWarnings=\toks28
LaTeX Info: Redefining \GenericWarning on input line 433.
\sl@ErrorCount=\count282
\sl@ErrorNumber=\count283
\sl@ErrorCasualties=\count284
\sl@TempBOE=\toks29
\sl@BankOfErrors=\toks30
LaTeX Info: Redefining \GenericError on input line 601.
) (f:/dev/texlive/2024/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count285
) (f:/dev/texlive/2024/texmf-dist/tex/latex/caption/caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)
 (f:/dev/texlive/2024/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen165
\captionmargin=\dimen166
\caption@leftmargin=\dimen167
\caption@rightmargin=\dimen168
\caption@width=\dimen169
\caption@indent=\dimen170
\caption@parindent=\dimen171
\caption@hangindent=\dimen172
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count286
\c@continuedfloat=\count287
) (f:/dev/texlive/2024/texmf-dist/tex/latex/caption/subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)
Package caption Info: New subtype `subfigure' on input line 238.
\c@subfigure=\count288
Package caption Info: New subtype `subtable' on input line 238.
\c@subtable=\count289
) (f:/dev/texlive/2024/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
CVPR 8.5 x 11-Inch Proceedings Style `cvpr.sty'.
\cvprrulerbox=\box53
\cvprrulercount=\count290
\cvprruleroffset=\dimen173
\cv@lineheight=\dimen174
\cv@boxheight=\dimen175
\cv@tmpbox=\box54
\cv@refno=\count291
\cv@tot=\count292
\cv@tmpc@=\count293
\cv@tmpc=\count294
(f:/dev/texlive/2024/texmf-dist/tex/latex/lineno/lineno.sty
Package: lineno 2023/05/20 line numbers on paragraphs v5.3
Invalid UTF-8 byte or sequence at line 292 replaced by U+FFFD.
 (f:/dev/texlive/2024/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
 (f:/dev/texlive/2024/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (f:/dev/texlive/2024/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
))
\linenopenalty=\count295
\output=\toks31
\linenoprevgraf=\count296
\linenumbersep=\dimen176
\linenumberwidth=\dimen177
\c@linenumber=\count297
\c@pagewiselinenumber=\count298
\c@LN@truepage=\count299
\c@internallinenumber=\count300
\c@internallinenumbers=\count301
\quotelinenumbersep=\dimen178
\bframerule=\dimen179
\bframesep=\dimen180
\bframebox=\box55
\linenoamsmath@ams@eqpen=\count302
LaTeX Info: Redefining \\ on input line 3180.
) (f:/dev/texlive/2024/texmf-dist/tex/latex/enumitem/enumitem.sty
Package: enumitem 2019/06/20 v3.9 Customized lists
\labelindent=\skip55
\enit@outerparindent=\dimen181
\enit@toks=\toks32
\enit@inbox=\box56
\enit@count@id=\count303
\enitdp@description=\count304
)) (./preamble.tex) (f:/dev/texlive/2024/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-01-20 v7.01h Hypertext links for LaTeX
 (f:/dev/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
) (f:/dev/texlive/2024/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (f:/dev/texlive/2024/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (f:/dev/texlive/2024/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (f:/dev/texlive/2024/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)) (f:/dev/texlive/2024/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (f:/dev/texlive/2024/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (f:/dev/texlive/2024/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (f:/dev/texlive/2024/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (f:/dev/texlive/2024/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count305
)
\@linkdim=\dimen182
\Hy@linkcounter=\count306
\Hy@pagecounter=\count307
 (f:/dev/texlive/2024/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-01-20 v7.01h Hyperref: PDFDocEncoding definition (HO)
) (f:/dev/texlive/2024/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count308
 (f:/dev/texlive/2024/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-01-20 v7.01h Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `breaklinks' set `true' on input line 4062.
Package hyperref Info: Option `colorlinks' set `true' on input line 4062.
Package hyperref Info: Hyper figures OFF on input line 4179.
Package hyperref Info: Link nesting OFF on input line 4184.
Package hyperref Info: Hyper index ON on input line 4187.
Package hyperref Info: Plain pages OFF on input line 4194.
Package hyperref Info: Backreferencing ON on input line 4197.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4446.
 (f:/dev/texlive/2024/texmf-dist/tex/latex/hyperref/backref.sty
Package: backref 2023-11-26 v1.44 Bibliographical back referencing
 (f:/dev/texlive/2024/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (f:/dev/texlive/2024/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
) (f:/dev/texlive/2024/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
 (f:/dev/texlive/2024/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
))
\c@Hy@tempcnt=\count309
LaTeX Info: Redefining \url on input line 4784.
\XeTeXLinkMargin=\dimen183
 (f:/dev/texlive/2024/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
)
\Fld@menulength=\count310
\Field@Width=\dimen184
\Fld@charsize=\dimen185
Package hyperref Info: Hyper figures OFF on input line 6063.
Package hyperref Info: Link nesting OFF on input line 6068.
Package hyperref Info: Hyper index ON on input line 6071.
Package hyperref Info: backreferencing ON on input line 6076.
Package hyperref Info: Link coloring ON on input line 6081.
Package hyperref Info: Link coloring with OCG OFF on input line 6088.
Package hyperref Info: PDF/A mode OFF on input line 6093.
 (f:/dev/texlive/2024/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count311
\c@Item=\count312
\c@Hfootnote=\count313
)
Package hyperref Info: Driver (autodetected): hxetex.
 (f:/dev/texlive/2024/texmf-dist/tex/latex/hyperref/hxetex.def
File: hxetex.def 2024-01-20 v7.01h Hyperref driver for XeTeX
 (f:/dev/texlive/2024/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\pdfm@box=\box57
\c@Hy@AnnotLevel=\count314
\HyField@AnnotCount=\count315
\Fld@listcount=\count316
\c@bookmark@seq@number=\count317
\Hy@SectionHShift=\skip56
) (f:/dev/texlive/2024/texmf-dist/tex/latex/cleveref/cleveref.sty
Package: cleveref 2018/03/27 v0.21.4 Intelligent cross-referencing
Package cleveref Info: `hyperref' support loaded on input line 2370.
LaTeX Info: Redefining \cref on input line 2370.
LaTeX Info: Redefining \Cref on input line 2370.
LaTeX Info: Redefining \crefrange on input line 2370.
LaTeX Info: Redefining \Crefrange on input line 2370.
LaTeX Info: Redefining \cpageref on input line 2370.
LaTeX Info: Redefining \Cpageref on input line 2370.
LaTeX Info: Redefining \cpagerefrange on input line 2370.
LaTeX Info: Redefining \Cpagerefrange on input line 2370.
LaTeX Info: Redefining \labelcref on input line 2370.
LaTeX Info: Redefining \labelcpageref on input line 2370.
Package cleveref Info: always capitalise cross-reference names on input line 7830.
Package cleveref Info: always capitalise cross-reference names on input line 7852.
)
LaTeX Font Info:    Trying to load font information for TU+ptm on input line 48.
LaTeX Font Info:    No file TUptm.fd. on input line 48.


LaTeX Font Warning: Font shape `TU/ptm/m/n' undefined
(Font)              using `TU/lmr/m/n' instead on input line 48.

(f:/dev/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-02-20 L3 backend support: XeTeX
\g__graphics_track_int=\count318
\l__pdf_internal_box=\box58
\g__pdf_backend_object_int=\count319
\g__pdf_backend_annotation_int=\count320
\g__pdf_backend_link_int=\count321
) (./main.aux)
\openout1 = `main.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 48.
LaTeX Font Info:    ... okay on input line 48.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 48.
LaTeX Font Info:    ... okay on input line 48.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 48.
LaTeX Font Info:    ... okay on input line 48.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 48.
LaTeX Font Info:    ... okay on input line 48.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 48.
LaTeX Font Info:    Trying to load font information for TS1+cmr on input line 48.
 (f:/dev/texlive/2024/texmf-dist/tex/latex/base/ts1cmr.fd
File: ts1cmr.fd 2023/04/13 v2.5m Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 48.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 48.
LaTeX Font Info:    ... okay on input line 48.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 48.
LaTeX Font Info:    ... okay on input line 48.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 48.
LaTeX Font Info:    ... okay on input line 48.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 48.
LaTeX Font Info:    ... okay on input line 48.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 48.
LaTeX Font Info:    ... okay on input line 48.
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: hyperref package is loaded.
Package caption Info: End \AtBeginDocument code.
 ABD: EveryShipout initializing macros
Package backref Info: ** backref set up for natbib ** on input line 48.
Package hyperref Info: Link coloring ON on input line 48.
 (./main.out) (./main.out)
\@outlinefile=\write3
\openout3 = `main.out'.



LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/m/n' instead on input line 50.

LaTeX Font Info:    Trying to load font information for U+msa on input line 50.
(f:/dev/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 50.
 (f:/dev/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
) (./sec/0_abstract.tex

LaTeX Font Warning: Font shape `TU/ptm/m/it' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1.

) (./sec/1_introduction.tex
LaTeX Font Info:    Trying to load font information for TU+phv on input line 11.
LaTeX Font Info:    No file TUphv.fd. on input line 11.


LaTeX Font Warning: Font shape `TU/phv/m/n' undefined
(Font)              using `TU/lmr/m/n' instead on input line 11.

[1


]) (./sec/2_background.tex) (./sec/3_method.tex [2]
Missing character: There is no 空 ("7A7A) in font ptmb!
Missing character: There is no 间 ("95F4) in font ptmb!
Missing character: There is no 一 ("4E00) in font ptmb!
Missing character: There is no 致 ("81F4) in font ptmb!
Missing character: There is no 的 ("7684) in font ptmb!
[3]
Missing character: There is no ​ (U+200B) in font [lmroman10-regular]:mapping=tex-text;!
Missing character: There is no ​ (U+200B) in font [lmroman10-regular]:mapping=tex-text;!

LaTeX Warning: Reference `fig:training' on page 4 undefined on input line 38.


Package natbib Warning: Citation `chen2021semi' on page 4 undefined on input line 42.

[4]) (./main.bbl (./main.brf)
\tf@brf=\write4
\openout4 = `main.brf'.

 [5])

Package natbib Warning: There were undefined citations.

[6

] (./main.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
 ***********


LaTeX Font Warning: Some font shapes were not available, defaults substituted.


LaTeX Warning: There were undefined references.

Package rerunfilecheck Info: File `main.out' has not changed.
(rerunfilecheck)             Checksum: 4FB2B3D6030DF44FA40CC38AB5211A07;1502.

Package rerunfilecheck Warning: File `main.brf' has changed.
(rerunfilecheck)                Rerun to get bibliographical references right.

Package rerunfilecheck Info: Checksums for `main.brf':
(rerunfilecheck)             Before: 17C021FAD58F76BBB4F0357011877DD2;1757
(rerunfilecheck)             After:  DA3A9F9E5CC3BA6F6DD8C41E29D2C402;1816.
 ) 
Here is how much of TeX's memory you used:
 14704 strings out of 474773
 243972 string characters out of 5759509
 1959842 words of memory out of 5000000
 36663 multiletter control sequences out of 15000+600000
 571303 words of font info for 89 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 87i,13n,93p,1973b,545s stack positions out of 10000i,1000n,20000p,200000b,200000s

Output written on main.pdf (6 pages).
