1. 微调SAM以适应EM通用数据
2. 微调SAVM以aware of SDF
3. (Quick Adapt)在任何 (1中) Fewshot能力能覆盖的数据上训练UNet
4. (即插即用) 将训练好的 UNet 插入 (2中) 进行分割

三维没有像SAM一样的大模型

0. Overview(NO)
1. SAM2简述
------Three main components?
2. 空间一致prompt free的AUTO-SAM
3. Fewshot
4. 全监督UNetSDF+多任务学习用于训练+Loss



### introduction
电子显微镜（EM）成像因其纳米级分辨率，已成为神经科学与细胞生物学中解析组织和细胞器超微结构的核心工具。随着聚焦离子束扫描电子显微镜（FIB-SEM）等成像设备的发展，研究者能够快速采集大规模、高分辨率的三维EM数据。这帮助了研究人员发现亚细胞水平的形态特征或深入揭示背后的内在机制。要从这些数据中获得生物学洞见，前提是对其中的细胞及细胞器进行精确的自动化分割。然而，大规模、高分辨率的三维EM数据为图像分割带来了巨大的挑战。规模庞大的数据集使得传统的或半自动化的手动标注方法变得不切实际且耗时巨大。其次，EM图像中待分割的对象（如轴突、细胞、线粒体和突触）通常具有复杂的形态、模糊的边界，并且广泛分布于整个空间域，这对精确定位和边界划分提出了严峻考验。

在三维生物图像分割的研究中，卷积神经网络（CNN）和U-Net结构被广泛应用。经典的3D U-Net及其改进网络在细胞和细胞器分割上取得了显著进展。随着长程依赖建模需求的增加，Transformer 被引入分割网络，在医学和生物图像中表现出更强的上下文感知能力。然而，这些方法均依赖大量人工标注，且在大规模三维高分辨率场景下往往面临计算开销过大和空间一致性不足的问题。另一方面，半监督和自监督学习被广泛探索以缓解标注成本问题。典型方法包括基于一致性正则化的伪标签学习、对比学习、以及自监督预训练。这些方法在减少标注需求方面取得了进展，但在复杂三维结构的空间连续性建模上仍有不足。

近期提出的通用分割模型 SAM 展现了令人瞩目的零样本与少样本能力，并被初步应用于生物医学图像分割[]。然而，这类方法主要基于独立的二维切片进行分割，因此在跨切片的空间连续性上存在不足，难以保证结构的完整性和连贯性。。SAM的后续版本 SAM2 与一些其他 SAM 变体如 SAM4EM 将 Memory 机制引入模型，提供了一定的空间连续性，但其 Memory 仍是依赖于自身的历史帧的分割结果，并不能显式的获取分割对象的三维表达。

在此背景下，本文提出了一种面向高分辨率三维EM图像的全新分割框架。我们的主要贡献可以总结如下：
首先，我们提出了一种基于 SAM2 的三维分割模型，通过引入 SDF（Signed Distance Function）作为记忆机制，有效提升了跨切片的空间一致性。其次，我们设计了一种结合 U-Net SDF 与 SAM2 的联合训练方法，充分利用大模型的 few-shot 能力，使模型能够在极少量标注条件下快速适配新的数据集。最后，我们开发了一套完整的工具链，覆盖从交互式标注到全自动分割的全过程，为大规模三维EM数据的研究提供了高效而实用的解决方案。

我们在多个有挑战性的大规模三维EM数据集上进行了广泛的实验，包括线粒体于细胞核的标注数据（）。我们的实验结果表明，我们的方法在使用比绝大多数半监督方法更少的标注数据的情况下，其与最先进的半监督甚至全监督方法相当或更好的分割性能。





Our main contributions can be summarized as follows:
A SAM2-based model that is designed for high resolution 3D electron microscopy (EM) image segmentation, featuring a SDF memory enabling better spatial coherence.
A fewshot learning method based on the Fewshot capability of large models to finetune to new datasets, where UnetSDF and SAM2 are trained jointly.
A toolset that supports the entire process from interactive annotation to fully automatic segmentation.

### Related Work

电镜图像分割方法

传统的电镜图像分割主要依赖阈值分割、eigenvector analysis、hierarchical region merging from the watershed algorithm等技术。然而，这些传统方法对复杂结构往往难以适用，近年来深度学习成为分割领域的主流方法。基于卷积神经网络（CNN）的端到端分割模型（如FCN、U-Net）已被广泛应用于细胞和细胞器的分割任务（应用）。U-Net通过对称的编码-解码结构和跳跃连接，实现了多尺度上下文信息的融合，从而显著提升了分割精度。针对三维EM数据，研究者发展了3D U-Net和V-Net等三维卷积网络，并应用与EM数据分割。三维网络可以直接在体素级别学习体积特征，但维度增加会带来计算量增加与输入分辨率的限制。此外，还出现了混合二维-三维网络、Flood-Filling网络等方法。随着对长程上下文依赖的需求增加，Transformer等基于自注意力机制的网络也被引入医学和生物图像分割中（应用），它们在捕获远距离像素关系和全局上下文方面展现了优异性能。总之，深度学习模型通过自动提取特征，提升了分割精度，但通常需要大量标注数据和高昂计算资源，且对高分辨率体积数据的分割仍然具有挑战性。

半监督学习方法被广泛探索以降低标注成本。在半监督学习中，常用伪标签传播和一致性正则化等策略来利用未标注数据。例如，Takaya等（2021）提出“4S”方法，通过利用体积中相邻切片的关联性，迭代生成伪标签来扩充标注集；Wolny等（2022）针对线粒体分割，将未标注区域视作背景，并在两条数据增强路径上引入嵌入一致性损失和推拉损失，以在嵌入空间中强化不同实例的可区分性。Mai 等（2023）实现了一个端到端“双可靠”网络，包括可靠像素聚合和原型选择模块，实现线粒体分割的半监督学习。在自监督预训练方面，Conrad和Narayan（2021）使用对比学习在大规模未标注细胞EM图像集上预训练网络，然后通过微调完成具体分割任务。这些方法在一定程度上缓解了标注需求，但由于它们都基于二维切片进行分割，对于复杂三维结构的空间连续性建模仍存在不足，容易出现切片间断裂或细微结构遗漏的问题。

SAM/SAM2及其在生物医学图像中的应用

Segment Anything Model（SAM）是Meta AI在2023年提出的一种通用分割基模型，其核心是一个基于Vision Transformer的图像编码器和一个提示式掩码解码器。SAM无需针对每个任务设计网络结构，只需通过点、框或文本等“提示”即可快速产生目标分割，展现了强大的零样本（zero-shot）和少样本适应能力。在生物医学图像领域，多项工作尝试将SAM引入到具体任务中。例如，Ma等人针对医学图像分割提出了MedSAM，通过在多模态医学数据集上对SAM进行微调，显著提升了分割性能。在显微镜图像分析方面，Archit等人提出μSAM框架，对SAM进行专门微调以适应光学显微和电镜数据，结果显示其在不同成像条件下显著改善了分割质量。这些研究通常基于二维策略：它们将体积数据视为一系列独立的图像切片进行分割，难以直接建模切片间的空间关联性。

最近推出的SAM2模型（2024）在此基础上进行了升级：采用了带有流式记忆机制的Transformer架构，并引入大规模视频分割数据，通过更高效的设计实现了图像分割任务上较SAM更高的准确度和6倍的加速。Shah等人通过动量更新的方法实现3D记忆，结合LoRA对模型进行微调。这些方法在分割切片时，一定程度上能够参考历史切片的分割结果与图像特征，但单一历史切片分割结果的瑕疵会影响模型对物体整体的理解，导致在传播过程中分割准确率下降。我们物体的三维表达参与memory的编码中以解决这一问题。



The Segment Anything (SA) project introduced a significant advancement in this field by proposing a foundation model for promptable image segmentation. SAM is designed to generate a valid segmentation mask for "anything" in an image, given various prompts such as points, bounding boxes, or even free-form text. A key design choice is its ability to handle ambiguity by predicting multiple masks for a single prompt, which allows for natural adaptation to different interpretations of a prompt. The model architecture consists of a powerful image encoder, a flexible prompt encoder, and a lightweight mask decoder, enabling efficient mask prediction in amortized real-time. SAM's impressive zero-shot transfer capabilities across a wide range of tasks and image distributions, often competitive with or superior to fully supervised methods, demonstrated its potential as a general-purpose segmentation model. Its training leveraged a large-scale dataset (SA-1B) containing over 1 billion masks on 11 million licensed and privacy-preserving images, collected using a sophisticated data engine.

Building upon the success of SAM for images, the Segment Anything Model 2 (SAM 2) extends promptable visual segmentation to both images and videos, aiming to be a unified solution. Recognizing the dynamic nature of the real world captured in videos, SAM 2 introduces the Promptable Visual Segmentation (PVS) task, generalizing image segmentation to the temporal domain. It addresses the need for temporal localization beyond static image segments, which is crucial for applications in AR/VR, robotics, and video editing. SAM 2 is designed for real-time video processing and demonstrates superior performance, offering better accuracy with 3× fewer interactions than prior video segmentation approaches, and is notably 6× faster than the original SAM for image segmentation. This evolution is supported by the collection of the SA-V dataset, the largest video segmentation dataset to date, acquired through an improved data engine that utilizes the model in a human-in-the-loop annotation process.

SAM提供AMG的方法以自动地对图中的所有可能实例进行分割，但这样分割的实例可能来自不同类，需要额外的分类操作；此外将AMG应用到SAM2的视频分割中时，无法分割出传播途中在非条件帧新出现的实例。为了使将为提示分割设计的SAM应用到全自动语义分割中，最常见的方法是将SAM的decoder替换为无需提示输入的类UNet的decoder如cellpose、μsam，也有方法通过输入图像自动生成提示（Auto-SAM）[需要更细地阐述]。但这样的方法基本都需要在特定数据集上以全监督的方式进行微调以适配特定的分割任务，尤其当新decoder较为heavy的情况下，训练整个SAM是较为困难且消耗资源的。



### Method
#### SAM2简述
SAM 2 is developed as a unified foundation model for promptable visual segmentation in both images and videos, generalizing the Segment Anything (SA) task to the temporal domain. While sharing core principles with SAM, SAM 2's capacity for video processing is crucially enabled by its enhanced memory mechanism.

The SAM 2 architecture comprises:
*   **Image Encoder**: This component processes video frames in a streaming fashion, providing unconditioned feature embeddings for each frame. It uses an MAE pre-trained Hiera image encoder, which supports multiscale features for decoding.
*   **Prompt Encoder**: Identical to SAM's, it encodes prompts such as clicks (positive/negative), bounding boxes, or masks to define the object's extent in a frame.
*   **Mask Decoder**: Following SAM's design, it uses "two-way" Transformer blocks to update prompt and frame embeddings. It predicts multiple masks for ambiguous prompts to ensure valid outputs, a feature extended to video frames where ambiguity can persist over time. An occlusion prediction head is also included to forecast if the object of interest is visible in the current frame.

The **memory mechanism** is central to SAM 2's ability to track objects and maintain temporal coherence in videos:
*   **Memory Attention**: This module is critical for conditioning the current frame's features on information from past frames and previous predictions, as well as any new prompts. It consists of Transformer blocks that perform self-attention and then cross-attention to a "memory bank".
*   **Memory Bank**: This acts as a repository for historical object information. It stores spatial feature maps from past frames and information from previously prompted frames.
*   **Memory Encoder**: This component is responsible for generating the memory that populates the memory bank. It fuses the current output mask (derived from the mask decoder's prediction) with the unconditioned frame embedding from the image encoder. Specifically, the mask token from the mask decoder's output is used as an "object pointer token" and placed into the memory bank.
*   **Object Pointers**: These are lightweight vectors, derived from the mask decoder's output tokens, that provide high-level semantic information about the object being segmented. They are cross-attended to by the memory attention module, enhancing tracking performance.

This integrated memory architecture enables SAM 2 to effectively leverage temporal context, allowing for real-time video processing and coherent object tracking across frames, even in dynamic or occluded scenarios.

#### 空间一致prompt free的AUTO-SAM
我们的目标是将SAM应用到全自动语义分割中，同时为模型提供更丰富的三维信息以确保分割结果的空间一致性/连续性。根据xx节，SAM2的memory机制可以从前几帧-以及条件帧-中获取分割信息从而保持一定的时序一致性。因此一个比较common的想法是将三维图像沿某个轴分解成一系列二维切片，然后使用SAM2沿该轴向从前向后或从后往前逐切片进行分割，如[]，可以利用SAM2的memory机制在分割二维切片时利用传播方向反向的一定数量切片的特征与分割信息来保持一定的空间一致性。但是，SAM2的memory仅能提供传播方向反向有限数量切片的特征与分割信息，无法获得传播方向正向未分割的切片的特征与分割信息，这种有向性使得不同传播方向的选择对分割质量有较大的影响。此外，切片分解轴向的选择与条件帧的选取也会影响到分割质量[]。而一旦在传播途中出现分割错误，其错误的结果会记录在memory bank中，影响后续的分割，导致目标追踪失败。

为了避免以上问题，我们提出了一种能够保证空间一致的预计算三维memory机制，其核心思想是使用一个轻量的三维分割模型预先对降采样后的体积计算一个粗略的距离场，将其与图像特征通过memory encoder计算得到memory，SAM2的mask decoder从预计算的memory中获取信息以分割原始分辨率的三维图像。由于memory是预计算的，因此我们可以扩展memory bank的大小以包含当前分割切片前后双向切片的memory，不仅能获得更多的空间信息，同时不会受到传播方向的影响。又由于不使用二维切片预测结果作为memory，单个切片预测错误不会影响到接下来的切片预测，解决了目标追踪失败的问题。此外，利用SAM2在memory bank不为空时可以不需要提示输入即可根据memory以及输入图像对进行分割的特性，我们可以使用prompt free的方式进行全自动分割。

##### 三维SDF Memory
SAM2使用memory encoder将输出的mask logit与frame embedding融合来计算memory。Memory机制使得SAM2一定程度上能够参考历史切片的分割结果与图像特征，但单一历史切片分割结果的瑕疵会影响模型对物体整体的理解，导致在传播过程中分割准确率下降，我们的核心思路是将物体的三维表达引入memory中，使得SAM2能够获取更加整体的三维物体语义信息。多亏了我们用于预计算memory的三维分割模型3D-Unet，我们可以使用三维空间中的有符号距离场SDF来进行encode memory而不是mask logit。有符号距离场（Signed Distance Field, SDF）是一种表示几何体的方式，它为空间中的每个点存储到该几何体表面最近距离的值，其中距离的符号表示点位于几何体的内部还是外部，我们希望其形式上与logit相似，因此取外负内正，公式如下。

其中，。。。由于我们使用3D-UNet预测SDF栅格，SDF的距离最大值不应超过卷积的感受野，否则无法正确预测，此外阈值的设定也能一定程度上减少训练难度。相较logit，SDF可以提供更清晰的几何信息，并且即使在长度十分有限的memory bank中，模型也可间接地通过距离信息获取更大范围的大致空间信息。[如何更容易使人理解？]具体的，我们将UNet预测的三维SDF栅格上采样到原始图像分辨率，并与原始图像一同沿同一轴向切片，图像切片送入SAM2的image encoder中，得到特征图，与SDF切片一同送入memory encoder中，得到memory，存入memory bank中。在进行分割时，我们选取当前分割切片的前6帧与后6帧的memory与当前切片的特征图进行memory attention与mask decoder，得到分割结果。（为了防止UNet在当前切片上的较为粗糙的SDF影响SAM的精细分割，因此仅使用前后帧的memory而并不会使用当前帧的memory。）最后，3D-UNet预测的SDF栅格也可以帮助生成种子点，以便使用分水岭算法将语义分割转换为实例分割。[需要强调即使，因为仅作为encode memory使用，粗糙的SDF同样可以胜任]



#### 利用SAM2的Fewshot能力用少量标注数据训练Spatial-SAM
为了极大地减少人工标注成本以更高效地对大规模体电镜数据进行分割，我们基于SAM2的交互式标注功能与Fewshot能力，开发了一整套从标注到半监督训练的全流程方案以及对应的工具集。其整体流程如图x所示。具体而言，对于一个新的大规模电镜数据集，首先我们从整个数据集中选取一定数量的三维图像（尺寸D=W=H）作为训练集。我们将这些三维图像分解为二维切片，选取一定量的二维切片，使用SAM2对这些切片中的分割目标进行交互式分割，然后人工对其中的错误进行修正，再将这些切片的分割结果作为提示帧，SAM2根据这些提示帧对整个训练集进行分割以生成伪标签，最后我们使用这些伪标签来训练我们的Spatial-SAM模型。【这里用数学语言解释整个流程】

半监督方法如Takaya等人提出'4S'方法所采用的伪标签学习方法往往需要使用仅少量标注切片从零开始对模型训练以生成伪标签。我们的方法则可利用SAM2的fewshot能力，以极少的标注数据为伪标签学习生成质量更高的初始伪标签，从而使得训练更易收敛，降低了模型过拟合的风险。从CPS工作得到灵感，我们在训练Spatial-SAM时交叉地训练UNet模块与SAM2模块。与CPS不同，我们的UNet模块与SAM2模块所需完成的任务不同，因此我们独立的训练这两个模块。我们的训练流程如图x所示。首先，我们将通过SAM2得到的初始伪标签转换为三维SDF，用其来训练UNet模块，得到能够输出分割目标SDF的UNet模块。然后我们使用该UNet模块来预测整个训练集的SDF，将其转换为分割掩码作为新的伪标签，这些伪标签会与原始标注一起训练SAM2模块。进行一轮训练的Spatial-SAM可以在整个数据集上进行分割，得到新的伪标签，进行下一轮的训练，以此循环训练。值得注意的是，在训练SAM2模块的环节，UNet模块会为其提供SDF以编码为memory。为了更充分地利用原始标注数据（我们认为是可靠的Ground Truth）并减少UNet生成的SDF出错对分割质量的影响，我们会以一个较高的概率p采样拥有原始标注的切片及其周围的切片，以1-p的概率采样体积中任意连续切片。且SAM2在分割切片时仅使用该切片前后6帧的SDF memory，不会使用当前切片的SDF memory，进一步地为了防止SAM过度依赖UNet生成的SDF信息。

我们使用MSE Loss作为主损失，使用Eikonal Loss保证SDF的合理性。训练UNet的损失函数为：
对于SAM2，我们沿用SAM2使用的损失函数，即Focal Loss、Dice Loss与IoU Loss与分类损失的加权和，公式如下：

其中Dice Loss与IoU Loss的作用，Focal Loss又能...

我们提供了一个工具集以便用户可以方便地执行从图像标注到自动分割的全流程。首先我们提供了交互式分割的功能，用户可以通过使用点或框在提示分割功能的帮助下进行快速的二维或三维图像注释。接下来我们提供了一个Fewshot小部件，用户可以通过简单的操作根据少量的二维切片注释来训练我们的UNet模块以适应新数据集。我们也提供全自动分割工具以快速调用mSAM对三维图像进行自动语义分割或实例分割，用户可以简单的为该任务指定即插即用的UNet模块以适应不同的分割任务。对于自动分割的结果，我们也支持使用交互式分割或手动修改的方式进行修改。最后，我们提供自定义设置允许用户根据自己的硬件与数据集来选择合适的工作环境与分辨率等。我们的工具集以napari插件的方式提供，napari是一款基于 Python 的多维图像数据查看器。

#### Fewshot
由于我们pipeline中的UNet模块是相对独立的，其接受三维图像输入，并输出对应分割物体的SDF。因此该模块可以做到即插即用，即对于任意一个分割任务，仅需将能够预测对应物体SDF的UNet模型替换即可。当然我们可以使用全监督的方式来训练这个独立的UNet模块，但是高分辨率的三维电镜图像通常很难提供足够的标注来进行三维神经网络的全监督训练。因此我们利用了SAM2的fewshot能力，以极少的标注数据即可训练该模块。具体而言，我们从整个数据集中选取一定数量的三维图像（尺寸D=W=H），这些图像之后将用作训练UNet的训练集。我们将这些三维图像分解为二维切片，将每隔一段距离的切片进行标注，然后将这些有标记的切片作为提示帧，使用SAM2(或在通用EM数据集上微调过的SAM2)[需要使读者意识到这里SAM2与我们pipeline的SAM不同]对这些体积进行预测。预测的结果将作为ground truth与原始图像一同来训练UNet。虽然使用SAM2预测结果作为ground truth不可避免的会有一些分割错误，但由于我们训练UNet的目的仅仅是为了给SAM2提供memory编码所需的SDF，实验表明即使粗糙的SDF也能胜任。为了防止切片分解轴向的选择对分割质量的影响，我们使用了正交分割[]，即对三个轴向都进行分解与预测，将结果的logits进行平均，这样的做法在大多数情况下可以得到比任意方向单独预测更好的结果。

#### 全监督UNetSDF+多任务学习用于训练+Loss
为了使我们的SAM能够适配修改后的memory机制，正确地从memory中获取信息并预测高质量的分割，同时提高其在各种体电镜数据上的分割能力，我们制定了一套用于微调mSAM的方案。我们在多个分割不同类型细胞器的多样数据集上对mSAM进行微调。由于一个UNet模块只能预测特定分割任务，因此我们独立地在每个数据集上训练一个UNet模块，并预计算其结果。训练UNet使用的损失函数为MSE Loss与Eikonal Loss的加权和，公式如下：

其中。。。mSAM的其他模块将在全部数据集上统一地进行微调。为了使mSAM更好地学习到使用SDF编码memory中SDF所带来的更多的几何信息，我们使用了一种多任务学习的方案。具体而言，我们[输入三维图像切片与UNet输出的预计算的SDF]扩展了mSAM的mask decoder，使其可以根据前后帧memory的信息同时预测当前帧的mask logit与SDF。由于预测SDF是相较预测分割mask更加困难的任务，在训练中加入该任务可以对模型有更强的约束以更好地利用memory中的SDF信息。对于预测的SDF，我们使用MSE Loss作为损失函数，对于预测的mask logit，我们沿用SAM2使用的损失函数，即Focal Loss、Dice Loss与IoU Loss与分类损失的加权和，公式如下：、

[谈谈几个loss？]

#### Toolset





### exp

我们使用pytorch实现Spatial-SAM，我们在一张NVIDIA RTX3090 GPU上对其进行训练。所有的三维图像都被缩放至1024 * 1024 * 1024的分辨率，各项异性的电镜图像被缩放至近似各项同性的尺寸。我们使用AdamW优化器，初始学习率为1e-4，训练100000步。

