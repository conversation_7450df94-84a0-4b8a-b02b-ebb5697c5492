@String(PAMI = {IEEE Trans. Pattern Anal. Mach. Intell.})
@String(IJCV = {Int. J. Comput. Vis.})
@String(CVPR= {IEEE Conf. Comput. Vis. Pattern Recog.})
@String(ICCV= {Int. Conf. Comput. Vis.})
@String(ECCV= {Eur. Conf. Comput. Vis.})
@String(NIPS= {Adv. Neural Inform. Process. Syst.})
@String(ICPR = {Int. Conf. Pattern Recog.})
@String(BMVC= {Brit. Mach. Vis. Conf.})
@String(TOG= {ACM Trans. Graph.})
@String(TIP  = {IEEE Trans. Image Process.})
@String(TVCG  = {IEEE Trans. Vis. Comput. Graph.})
@String(TMM  = {IEEE Trans. Multimedia})
@String(ACMMM= {ACM Int. Conf. Multimedia})
@String(ICME = {Int. Conf. Multimedia and Expo})
@String(ICASSP=	{ICASSP})
@String(ICIP = {IEEE Int. Conf. Image Process.})
@String(ACCV  = {ACCV})
@String(ICLR = {Int. Conf. Learn. Represent.})
@String(IJCAI = {IJCAI})
@String(PR   = {Pattern Recognition})
@String(AAAI = {AAAI})
@String(CVPRW= {IEEE Conf. Comput. Vis. Pattern Recog. Worksh.})
@String(CSVT = {IEEE Trans. Circuit Syst. Video Technol.})

@String(SPL	= {IEEE Sign. Process. Letters})
@String(VR   = {Vis. Res.})
@String(JOV	 = {J. Vis.})
@String(TVC  = {The Vis. Comput.})
@String(JCST  = {J. Comput. Sci. Tech.})
@String(CGF  = {Comput. Graph. Forum})
@String(CVM = {Computational Visual Media})


@String(PAMI  = {IEEE TPAMI})
@String(IJCV  = {IJCV})
@String(CVPR  = {CVPR})
@String(ICCV  = {ICCV})
@String(ECCV  = {ECCV})
@String(NIPS  = {NeurIPS})
@String(ICPR  = {ICPR})
@String(BMVC  =	{BMVC})
@String(TOG   = {ACM TOG})
@String(TIP   = {IEEE TIP})
@String(TVCG  = {IEEE TVCG})
@String(TCSVT = {IEEE TCSVT})
@String(TMM   =	{IEEE TMM})
@String(ACMMM = {ACM MM})
@String(ICME  =	{ICME})
@String(ICASSP=	{ICASSP})
@String(ICIP  = {ICIP})
@String(ACCV  = {ACCV})
@String(ICLR  = {ICLR})
@String(IJCAI = {IJCAI})
@String(PR = {PR})
@String(AAAI = {AAAI})
@String(CVPRW= {CVPRW})
@String(CSVT = {IEEE TCSVT})



@misc{Authors14,
  author = {FirstName LastName},
  title  = {The frobnicatable foo filter},
  note   = {Face and Gesture submission ID 324. Supplied as supplemental material {\tt fg324.pdf}},
  year   = 2014
}

@misc{Authors14b,
  author = {FirstName LastName},
  title  = {Frobnication tutorial},
  note   = {Supplied as supplemental material {\tt tr.pdf}},
  year   = 2014
}

@article{Alpher02,
  author  = {FirstName Alpher},
  title   = {Frobnication},
  journal = PAMI,
  volume  = 12,
  number  = 1,
  pages   = {234--778},
  year    = 2002
}

@article{Alpher03,
  author  = {FirstName Alpher and  FirstName Fotheringham-Smythe},
  title   = {Frobnication revisited},
  journal = {Journal of Foo},
  volume  = 13,
  number  = 1,
  pages   = {234--778},
  year    = 2003
}

@article{Alpher04,
  author  = {FirstName Alpher and FirstName Fotheringham-Smythe and FirstName Gamow},
  title   = {Can a machine frobnicate?},
  journal = {Journal of Foo},
  volume  = 14,
  number  = 1,
  pages   = {234--778},
  year    = 2004
}

@inproceedings{Alpher05,
  author    = {FirstName Alpher and FirstName Gamow},
  title     = {Can a computer frobnicate?},
  booktitle = CVPR,
  pages     = {234--778},
  year      = 2005
}






# 传统0特征向量分析
@article{FRANGAKIS2002105,
  title    = {Segmentation of two- and three-dimensional data from electron microscopy using eigenvector analysis},
  journal  = {Journal of Structural Biology},
  volume   = {138},
  number   = {1},
  pages    = {105-113},
  year     = {2002},
  issn     = {1047-8477},
  doi      = {https://doi.org/10.1016/S1047-8477(02)00032-1},
  url      = {https://www.sciencedirect.com/science/article/pii/S1047847702000321},
  author   = {Achilleas S Frangakis and Reiner Hegerl},
  abstract = {An automatic image segmentation method is used to improve processing and visualization of data obtained by electron microscopy. Exploiting affinity criteria between pixels, e.g., proximity and gray level similarity, in conjunction with an eigenvector analysis, the image is subdivided into areas which correspond to objects or meaningful regions. Extending a proposal by Shi and Malik (1997, Proceedings of the IEEE conference on Computer Vision and Pattern Recognition, pp. 731–737) the approach was adapted to the field of electron microscopy, especially to three-dimensional application as needed by electron tomography. Theory, implementation, parameter setting, and results obtained with a variety of data are presented and discussed. The method turns out to be a powerful tool for visualization with the potential for further improvement by developing and tuning new affinity.}
}




# 传统1分水岭
@inproceedings{liu2012watershed,
  title        = {Watershed merge tree classification for electron microscopy image segmentation},
  author       = {Liu, Ting and Jurrus, Elizabeth and Seyedhosseini, Mojtaba and Ellisman, Mark and Tasdizen, Tolga},
  booktitle    = {Proceedings of the 21st International Conference on Pattern Recognition (ICPR2012)},
  pages        = {133--137},
  year         = {2012},
  organization = {IEEE}
}




# FCN
@inproceedings{long2015fully,
  title     = {Fully convolutional networks for semantic segmentation},
  author    = {Long, Jonathan and Shelhamer, Evan and Darrell, Trevor},
  booktitle = {Proceedings of the IEEE conference on computer vision and pattern recognition},
  pages     = {3431--3440},
  year      = {2015}
}

# UNet
@inproceedings{ronneberger2015u,
  title        = {U-net: Convolutional networks for biomedical image segmentation},
  author       = {Ronneberger, Olaf and Fischer, Philipp and Brox, Thomas},
  booktitle    = {International Conference on Medical image computing and computer-assisted intervention},
  pages        = {234--241},
  year         = {2015},
  organization = {Springer}
}

# FCN UNET 应用
@article{cao2020denseunet,
  title     = {DenseUNet: densely connected UNet for electron microscopy image segmentation},
  author    = {Cao, Yue and Liu, Shigang and Peng, Yali and Li, Jun},
  journal   = {IET Image Processing},
  volume    = {14},
  number    = {12},
  pages     = {2682--2689},
  year      = {2020},
  publisher = {Wiley Online Library}
}
@article{quan2021fusionnet,
  title     = {Fusionnet: A deep fully residual convolutional neural network for image segmentation in connectomics},
  author    = {Quan, Tran Minh and Hildebrand, David Grant Colburn and Jeong, Won-Ki},
  journal   = {Frontiers in Computer Science},
  volume    = {3},
  pages     = {613981},
  year      = {2021},
  publisher = {Frontiers Media SA}
}

# 3D Unet
@inproceedings{cciccek20163d,
  title        = {3D U-Net: learning dense volumetric segmentation from sparse annotation},
  author       = {{\c{C}}i{\c{c}}ek, {\"O}zg{\"u}n and Abdulkadir, Ahmed and Lienkamp, Soeren S and Brox, Thomas and Ronneberger, Olaf},
  booktitle    = {International conference on medical image computing and computer-assisted intervention},
  pages        = {424--432},
  year         = {2016},
  organization = {Springer}
}

# V-Net
@inproceedings{milletari2016v,
  title        = {V-net: Fully convolutional neural networks for volumetric medical image segmentation},
  author       = {Milletari, Fausto and Navab, Nassir and Ahmadi, Seyed-Ahmad},
  booktitle    = {2016 fourth international conference on 3D vision (3DV)},
  pages        = {565--571},
  year         = {2016},
  organization = {Ieee}
}


# trans 应用
@inproceedings{pan2023adaptive,
  title     = {Adaptive template transformer for mitochondria segmentation in electron microscopy images},
  author    = {Pan, Yuwen and Luo, Naisong and Sun, Rui and Meng, Meng and Zhang, Tianzhu and Xiong, Zhiwei and Zhang, Yongdong},
  booktitle = {Proceedings of the IEEE/CVF international conference on computer vision},
  pages     = {21474--21484},
  year      = {2023}
}
@inproceedings{luo2024electron,
  title     = {Electron microscopy images as set of fragments for mitochondrial segmentation},
  author    = {Luo, Naisong and Sun, Rui and Pan, Yuwen and Zhang, Tianzhu and Wu, Feng},
  booktitle = {Proceedings of the AAAI Conference on Artificial Intelligence},
  volume    = {38},
  number    = {4},
  pages     = {3981--3989},
  year      = {2024}
}
@article{zhang2025swincell,
  title     = {SwinCell: a 3D transformer and flow-based framework for improved cell segmentation},
  author    = {Zhang, Xiao and Lin, Zihan and Wang, Liguo and Chu, Yong S and Yang, Yang and Xiao, Xianghui and Lin, Yuewei and Liu, Qun},
  journal   = {Communications Biology},
  volume    = {8},
  number    = {1},
  pages     = {962},
  year      = {2025},
  publisher = {Nature Publishing Group UK London}
}


# 半监督Takaya
@article{takaya2021sequential,
  title     = {Sequential semi-supervised segmentation for serial electron microscopy image with small number of labels},
  author    = {Takaya, Eichi and Takeichi, Yusuke and Ozaki, Mamiko and Kurihara, Satoshi},
  journal   = {Journal of Neuroscience Methods},
  volume    = {351},
  pages     = {109066},
  year      = {2021},
  publisher = {Elsevier}
}
# 半监督Wolny
@inproceedings{wolny2022sparse,
  title     = {Sparse object-level supervision for instance segmentation with pixel embeddings},
  author    = {Wolny, Adrian and Yu, Qin and Pape, Constantin and Kreshuk, Anna},
  booktitle = {Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition},
  pages     = {4402--4411},
  year      = {2022}
}
# 半监督Mai
@inproceedings{mai2023dualrel,
  title     = {Dualrel: Semi-supervised mitochondria segmentation from a prototype perspective},
  author    = {Mai, Huayu and Sun, Rui and Zhang, Tianzhu and Xiong, Zhiwei and Wu, Feng},
  booktitle = {Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages     = {19617--19626},
  year      = {2023}
}
# 半监督CPS
@inproceedings{chen2021semi,
  title     = {Semi-supervised semantic segmentation with cross pseudo supervision},
  author    = {Chen, Xiaokang and Yuan, Yuhui and Zeng, Gang and Wang, Jingdong},
  booktitle = {Proceedings of the IEEE/CVF conference on computer vision and pattern recognition},
  pages     = {2613--2622},
  year      = {2021}
}
# 自监督CEM500K
@article{conrad2021cem500k,
  title     = {CEM500K, a large-scale heterogeneous unlabeled cellular electron microscopy image dataset for deep learning},
  author    = {Conrad, Ryan and Narayan, Kedar},
  journal   = {Elife},
  volume    = {10},
  pages     = {e65894},
  year      = {2021},
  publisher = {eLife Sciences Publications, Ltd}
}

# SAM
@inproceedings{kirillov2023segment,
  title     = {Segment anything},
  author    = {Kirillov, Alexander and Mintun, Eric and Ravi, Nikhila and Mao, Hanzi and Rolland, Chloe and Gustafson, Laura and Xiao, Tete and Whitehead, Spencer and Berg, Alexander C and Lo, Wan-Yen and others},
  booktitle = {Proceedings of the IEEE/CVF international conference on computer vision},
  pages     = {4015--4026},
  year      = {2023}
}
# SAM2
@article{ravi2024sam,
  title   = {Sam 2: Segment anything in images and videos},
  author  = {Ravi, Nikhila and Gabeur, Valentin and Hu, Yuan-Ting and Hu, Ronghang and Ryali, Chaitanya and Ma, Tengyu and Khedr, Haitham and R{\"a}dle, Roman and Rolland, Chloe and Gustafson, Laura and others},
  journal = {arXiv preprint arXiv:2408.00714},
  year    = {2024}
}
# SAM4EM
@inproceedings{shah2025sam4em,
  title     = {SAM4EM: Efficient memory-based two stage prompt-free segment anything model adapter for complex 3D neuroscience electron microscopy stacks},
  author    = {Shah, Uzair and Agus, Marco and Boges, Daniya and Chiappini, Vanessa and Alzubaidi, Mahmood and Schneider, Jens and Hadwiger, Markus and Magistretti, Pierre J and Househ, Mowafa and Cal{\`\i}, Corrado},
  booktitle = {Proceedings of the Computer Vision and Pattern Recognition Conference},
  pages     = {4678--4687},
  year      = {2025}
}
# MedSAM
@article{ma2024segment,
  title     = {Segment anything in medical images},
  author    = {Ma, Jun and He, Yuting and Li, Feifei and Han, Lin and You, Chenyu and Wang, Bo},
  journal   = {Nature Communications},
  volume    = {15},
  number    = {1},
  pages     = {654},
  year      = {2024},
  publisher = {Nature Publishing Group UK London}
}
# MicroSAM
@article{archit2025segment,
  title     = {Segment anything for microscopy},
  author    = {Archit, Anwai and Freckmann, Luca and Nair, Sushmita and Khalid, Nabeel and Hilt, Paul and Rajashekar, Vikas and Freitag, Marei and Teuber, Carolin and Spitzner, Melanie and Tapia Contreras, Constanza and others},
  journal   = {Nature Methods},
  volume    = {22},
  number    = {3},
  pages     = {579--591},
  year      = {2025},
  publisher = {Nature Publishing Group US New York}
}


# OpenOrganelle 1
@article{xu2021open,
  title     = {An open-access volume electron microscopy atlas of whole cells and tissues},
  author    = {Xu, C Shan and Pang, Song and Shtengel, Gleb and M{\"u}ller, Andreas and Ritter, Alex T and Hoffman, Huxley K and Takemura, Shin-ya and Lu, Zhiyuan and Pasolli, H Amalia and Iyer, Nirmala and others},
  journal   = {Nature},
  volume    = {599},
  number    = {7883},
  pages     = {147--151},
  year      = {2021},
  publisher = {Nature Publishing Group UK London}
}
# OpenOrganelle 2
@article{heinrich2021whole,
  title     = {Whole-cell organelle segmentation in volume electron microscopy},
  author    = {Heinrich, Larissa and Bennett, Davis and Ackerman, David and Park, Woohyun and Bogovic, John and Eckstein, Nils and Petruncio, Alyson and Clements, Jody and Pang, Song and Xu, C Shan and others},
  journal   = {Nature},
  volume    = {599},
  number    = {7883},
  pages     = {141--146},
  year      = {2021},
  publisher = {Nature Publishing Group UK London}
}
# MitoEM
@inproceedings{wei2020mitoem,
  title        = {MitoEM dataset: large-scale 3D mitochondria instance segmentation from EM images},
  author       = {Wei, Donglai and Lin, Zudi and Franco-Barranco, Daniel and Wendt, Nils and Liu, Xingyu and Yin, Wenjie and Huang, Xin and Gupta, Aarush and Jang, Won-Dong and Wang, Xueying and others},
  booktitle    = {International Conference on Medical Image Computing and Computer-Assisted Intervention},
  pages        = {66--76},
  year         = {2020},
  organization = {Springer}
}
# Urocell 1
@article{ZerovnikMekuc2020,
  author  = {Manca {Žerovnik Mekuč} and Ciril Bohak and Samo Hudoklin and Byeong Hak Kim and Rok Romih and Min Young Kim and Matija Marolt},
  title   = {Automatic segmentation of mitochondria and endolysosomes in volumetric electron microscopy data},
  journal = {Computers in Biology and Medicine},
  volume  = {119},
  pages   = {103693},
  year    = {2020},
  issn    = {0010-4825},
  doi     = {https://doi.org/10.1016/j.compbiomed.2020.103693},
  url     = {https://www.sciencedirect.com/science/article/pii/S0010482520300792}
}
# Urocell 2
@article{ZerovnikMekuc2022,
  author  = {Manca {Žerovnik Mekuč} and Ciril Bohak and Eva Boneš and Samo Hudoklin and Rok Romih and Matija Marolt},
  title   = {Automatic segmentation and reconstruction of intracellular compartments in volumetric electron microscopy data},
  journal = {Computer Methods and Programs in Biomedicine},
  volume  = {223},
  pages   = {106959},
  year    = {2022},
  issn    = {0169-2607},
  doi     = {https://doi.org/10.1016/j.cmpb.2022.106959},
  url     = {https://www.sciencedirect.com/science/article/pii/S0169260722003418}
}

