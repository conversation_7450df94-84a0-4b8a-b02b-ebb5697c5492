@echo off
setlocal enabledelayedexpansion

:: LaTeX 快速编译脚本
:: 作者: AI Assistant
:: 用途: 自动编译 LaTeX 文档，处理参考文献和交叉引用

echo ========================================
echo       LaTeX 快速编译脚本
echo ========================================
echo.

:: 设置文档名称（不包含扩展名）
set DOCNAME=main

:: 检查 LaTeX 文件是否存在
if not exist "%DOCNAME%.tex" (
    echo 错误: 找不到 %DOCNAME%.tex 文件！
    echo 请确保脚本在正确的目录中运行。
    pause
    exit /b 1
)

echo 开始编译 %DOCNAME%.tex...
echo.

:: 第一次编译 - 生成 .aux 文件
echo [1/4] 第一次 pdflatex 编译...
pdflatex -interaction=nonstopmode "%DOCNAME%.tex"
if !errorlevel! neq 0 (
    echo 错误: 第一次 pdflatex 编译失败！
    echo 请检查 LaTeX 语法错误。
    pause
    exit /b 1
)

:: 检查是否有 .bib 文件，如果有则运行 bibtex
if exist "%DOCNAME%.bib" (
    echo [2/4] 运行 bibtex 处理参考文献...
    bibtex "%DOCNAME%"
    if !errorlevel! neq 0 (
        echo 警告: bibtex 处理可能有问题，但继续编译...
    )
) else (
    echo [2/4] 未找到 .bib 文件，跳过 bibtex...
)

:: 第二次编译 - 处理参考文献
echo [3/4] 第二次 pdflatex 编译...
pdflatex -interaction=nonstopmode "%DOCNAME%.tex"
if !errorlevel! neq 0 (
    echo 错误: 第二次 pdflatex 编译失败！
    pause
    exit /b 1
)

:: 第三次编译 - 确保所有引用正确
echo [4/4] 第三次 pdflatex 编译（最终）...
pdflatex -interaction=nonstopmode "%DOCNAME%.tex"
if !errorlevel! neq 0 (
    echo 错误: 最终 pdflatex 编译失败！
    pause
    exit /b 1
)

echo.
echo ========================================
echo           编译完成！
echo ========================================

:: 检查 PDF 是否生成
if exist "%DOCNAME%.pdf" (
    echo ✓ PDF 文件已生成: %DOCNAME%.pdf
    
    :: 获取文件大小
    for %%A in ("%DOCNAME%.pdf") do set FILESIZE=%%~zA
    echo ✓ 文件大小: !FILESIZE! 字节
    
    :: 询问是否打开 PDF
    echo.
    set /p OPEN_PDF="是否要打开 PDF 文件？(y/n): "
    if /i "!OPEN_PDF!"=="y" (
        start "" "%DOCNAME%.pdf"
    )
) else (
    echo ✗ 错误: PDF 文件未生成！
)

echo.
echo 编译日志保存在: %DOCNAME%.log
echo 如有错误，请查看日志文件。

:: 询问是否清理临时文件
echo.
set /p CLEANUP="是否要清理临时文件？(y/n): "
if /i "!CLEANUP!"=="y" (
    echo 清理临时文件...
    del /q "%DOCNAME%.aux" 2>nul
    del /q "%DOCNAME%.bbl" 2>nul
    del /q "%DOCNAME%.blg" 2>nul
    del /q "%DOCNAME%.brf" 2>nul
    del /q "%DOCNAME%.out" 2>nul
    del /q "%DOCNAME%.synctex.gz" 2>nul
    echo ✓ 临时文件已清理
)

echo.
echo 按任意键退出...
pause >nul
