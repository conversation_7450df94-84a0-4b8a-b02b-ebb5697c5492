### MC-Net：基于不确定性的左心房分割
"Semi-supervised Left Atrium Segmentation with Mutual Consistency Training" (半监督左心房分割与互学习一致性训练) 这篇文章提出了一种名为 MC-Net 的新型半监督学习框架，用于解决医学图像分割中像素级标注数据稀缺的问题 [1, 2]。
该文章的核心思想和方法如下：
#### 核心问题与挑战
◦ 深度学习算法在医学图像分割中高度依赖于大量有标注的数据，而获取这些数据（特别是像素级精细标注）是昂贵、耗时且需要专业知识的 [1, 3, 4]。
◦ 在训练数据有限的情况下，现有方法往往低估了复杂或模糊区域（如小分支或模糊边缘）的重要性，导致模型在这些挑战性区域的预测模糊不清 [1, 5]。
#### MC-Net 的目标与核心理念
◦ MC-Net 旨在通过有效利用未标注数据来提升分割性能 [1]。
◦ 它特别强调模型对挑战性区域的预测不确定性，并认为这些区域包含更多关键信息，应在训练过程中予以强调，以减少模型的认识不确定性（epistemic uncertainty）并学习更具泛化能力的特征表示 [5-8]。
#### MC-Net 框架构成
◦ MC-Net 包含一个编码器 (encoder) 和两个略有不同的解码器 (decoders) [实际上就是反卷积换成了三线性插值]。这种双解码器设计允许模型通过两个输出之间的预测差异来捕获不确定性信息 [6]。
◦ 它引入了一种新颖的循环伪标签方案 (cycled pseudo label scheme) 来利用这种不确定性信息，以促进相互一致性训练 (mutual consistency training) [1, 6, 7]。
#### 训练过程详解
◦ 监督损失 (Lseg)： * 仅应用于有标注数据。 * 使用 Dice 损失来计算两个解码器（PA 和 PB）预测结果与真实标注 (Y) 之间的误差 [9]。
◦ 非监督损失 (Lc - 一致性损失)： * 应用于所有训练数据（包括有标注和未标注数据） [9]。 * 通过以下步骤实现： * 生成软伪标签 (Soft Pseudo Labels)：使用一个锐化函数 (sharpening function) [6, 10] 对解码器的概率输出进行处理，生成软伪标签。这些软伪标签有助于正则化熵 (entropy regularization) [6, 10]，并能有效减少错误伪标签的影响 [10]。 * 互学习一致性 (Mutual Consistency)：循环伪标签方案促使两个解码器相互学习。具体来说，解码器 A 的软伪标签 (sPLA) 用于监督解码器 B 的训练，反之亦然 (sPLB 监督 PA) [10]。 * 损失计算：一致性损失 (Lc) 通过计算两个解码器预测结果与相应软伪标签之间的 L2 损失（均方误差） 来实现 [9]。这种一致性约束鼓励模型对相同输入产生一致且低熵的预测，从而使模型更加关注未标注数据中的挑战性区域 [6, 10]。
◦ 总损失：最终训练损失是监督损失和非监督一致性损失的加权和，其中 λ 是平衡两部分贡献的超参数 [9]。
![alt text](<屏幕截图 2025-07-08 151102.png>)



### Semi-supervised Meta-learning with Disentanglement for Domain-Generalised Medical Image Segmentation
（看看元学习是什么）
该文章《Semi-supervised Meta-learning with Disentanglement for Domain-Generalised Medical Image Segmentation》提出了一种**半监督元学习框架，结合了特征解耦，以解决医学图像分割中模型泛化到未见域的挑战**。

以下是该方法的详细介绍：

*   **核心问题**
    *   深度学习模型在医学图像分割中的泛化能力是一个重大挑战，因为它们通常在特定数据集上训练，但实际应用中数据可能来自不同的医疗中心、扫描仪或采用不同的采集设置。
    *   这种数据统计上的差异被称为“域偏移”（domain shifts），可能由患者群体、扫描仪和扫描采集设置的变化引起，从而影响图像的亮度、对比度等特征。
    *   为每个新域手动标注大量像素级数据既耗时又昂贵，因此需要一种能够利用有限标注数据并泛化到新域的方法。

*   **提出的框架：半监督解耦元学习 (Semi-supervised Meta-learning with Disentanglement)**
    该方法旨在学习能够捕获任务相关信息，同时对特定域信息不敏感的特征表示。

    *   **模型架构**
        *   该框架包含一个**特征网络** (Fψ)，用于提取**解耦后的解剖特征** (Z)。
        *   一个**任务网络** (Tθ)，用于基于Z**预测分割掩膜**。
        *   **解耦网络**：用于将输入图像X分解为**共享的（外观）表示** (s) 和**域特定的表示** (d)。
        *   一个**域分类器** (TC(d))，用于预测输入图像的源域标签。
        *   一个**解码器** (DE)，将提取的特征Z与表示s和d结合，以**重建输入图像**。解码器中使用了**自适应实例归一化** (AdaIN) 层，以促进解耦，使得Z编码空间等变（解剖）信息，而s和d仅编码通用或特定于域的外观。

    *   **元学习机制 (Gradient-Based Meta-Learning)**
        *   该方法采用**梯度基元学习**，通过在训练的每次迭代中将源域数据D随机划分为**元训练集** (Dtr) 和**元测试集** (Dte) 来**模拟域偏移**。
        *   模型通过一个元训练步骤和一个元测试步骤进行训练。
        *   **元训练目标**旨在优化模型参数，使其不仅在源域上表现良好，而且其未来的更新也能很好地泛化到未见域。元训练损失包含分割任务损失和解耦任务损失。
        *   **元测试目标**则要求模型能够准确预测分割掩膜，并将Z、s和d解耦到与元训练集相同的水平。文章指出，**重建质量**（与真实图像X对比）和**域分类准确性**（与真实标签c对比）可以作为解耦水平的代理指标。

    *   **解耦学习 (Learning Disentangled Representations)**
        *   该方法**显式地解耦与域偏移相关的表示**。它旨在区分两种类型的偏移：由扫描仪和采集设置变化引起的偏移，以及由患者群体变化引起的偏移。
        *   目标是使模型对解剖结构的变化敏感，而对成像特征（无论是跨域共享的还是域特定的）的变化不敏感。
        *   **空间（网格状）特征Z**被用来表示解剖结构。
        *   **两个向量s和d**被用来编码通用和域特定的成像特征。
        *   通过**重建方法**来实现解耦，这使得模型能够**利用无标签数据来更好地模拟真实的域偏移**。
        *   **低秩正则化**被用作学习偏差，以促进更好的解耦和提高泛化性能。

    *   **半监督特性**
        *   该框架能够**利用无标签数据**进行训练，这对于数据标注成本高昂的医学图像领域尤其重要。
        *   通过解耦方法中的重建机制，无标签数据得以有效利用，从而更好地模拟真实域偏移，提升模型泛化性能，尤其是在标注数据稀缺的情况下。

总而言之，该方法通过在元学习框架中显式地结合特征解耦，并利用无标签数据来模拟和处理域偏移，从而在有限标注数据的情况下，提升医学图像分割模型在不同域间的泛化能力。


#### Efficient Semi-supervised Gross Target Volume of Nasopharyngeal Carcinoma Segmentation via Uncertainty Rectified Pyramid Consistency

该文章提出了一种名为**不确定性校正金字塔一致性（Uncertainty Rectified Pyramid Consistency, URPC）**的新型框架，用于鼻咽癌大体肿瘤体积（Gross Target Volume, GTV）的半监督分割。该方法旨在解决医学图像分割中标记数据稀缺的问题，通过有效地利用未标记数据来提高分割性能。

以下是该方法的主要组成部分：

*   **金字塔预测网络（Pyramid Prediction Network, PPNet）**：
    *   PPNet 扩展了主干分割网络（例如 3D UNet），使其能够生成**不同尺度的金字塔预测**。
    *   在解码器的每个上采样块之后，添加了一个预测层（由 1x1x1 卷积和 softmax 层实现）来生成多尺度预测。
    *   为了在网络中引入更多扰动，在每个预测层之前插入了 dropout 层和特征级噪声添加层。
    *   对于标记图像，PPNet 通过最小化传统的监督分割损失（包括 Dice 损失和交叉熵损失）进行训练。

*   **多尺度一致性损失（Multi-scale Consistency Loss）**：
    *   该方法的核心思想是，对于相同的输入，不同尺度的预测结果应该相似且一致。
    *   对于未标记图像，PPNet 通过鼓励多尺度预测之间的一致性来进行正则化。
    *   具体来说，它计算所有尺度的平均预测，并最小化每个尺度的预测与该平均预测之间的 L2 距离。

*   **不确定性校正模块（Uncertainty Rectifying Module）**：
    *   由于金字塔预测在不同尺度上具有不同的空间分辨率，直接强制像素级别的一致性可能导致细节丢失或模型崩溃。为了解决这个问题，URPC 引入了不确定性校正模块。
    *   **高效的不确定性估计**：该方法通过**单次前向传播**即可高效地估计不确定性，无需额外的计算开销。
        *   不确定性通过计算平均预测与每个尺度预测之间的 **KL 散度**来衡量。KL 散度值越大，表明该像素在该尺度的预测与其它尺度预测差异越大，不确定性越高。
    *   **不确定性校正**：基于估计出的不确定性图，URPC 进一步修正了金字塔一致性损失，以**强调可靠部分（低不确定性）并忽略不可靠部分（高不确定性）**，从而实现更稳定的无监督训练。
        *   对损失项应用权重 `wv_s = e^(-Dv_s)`，其中 `Dv_s` 是不确定性值，使得不确定性越高，权重越低。
        *   此外，还引入了**不确定性最小化项**，以鼓励 PPNet 生成更一致的预测。

通过这些组件，URPC 框架能够有效地利用未标记图像，并通过不确定性机制引导模型学习更可靠的知识，从而提高半监督分割的性能。该方法与现有方法相比，具有更高的效率，因为它只需要单次前向传播即可获得不确定性估计。

#### Few-Shot Domain Adaptation with Polymorphic Transformers

该文章提出了一种名为**多态Transformer (Polymorphic Transformer，简称Polyformer)** 的新型架构，旨在解决**少量样本域适应 (Few-Shot Domain Adaptation, Few-Shot DA)** 在医学图像分割任务中的挑战。

以下是该文章所使用方法的详细总结：

*   **核心思想与目标**
    *   该方法旨在**弥合不同领域之间的差距**（域适应），使在源域训练的模型能够在仅有少量标注的目标域数据的情况下，也能在目标域上表现良好。
    *   其核心在于将**域适应的责任转移到一个可插拔的Polyformer层**上，从而在适应新域时，可以保持原模型的绝大部分权重不变，显著减少过拟合的风险。

*   **Polyformer架构设计**
    *   **插入位置**：Polyformer层被插入到现有深度神经网络的**特征提取器（M1）和任务头（M2）之间**。例如，在一个U-Net模型中，它可以位于编码器-解码器（M1）和分割头（M2）之间。
    *   **内部结构**：Polyformer层由**两个子Transformer**组成（Transformer 1和Transformer 2）。
    *   **原型嵌入（Prototype Embeddings）**：
        *   它首先从源域数据中**提取一组“原型嵌入”**。这些原型是与输入无关的**持久性嵌入**，代表了源域特征的浓缩表示。
        *   这些原型通过压缩巨大的特征空间（例如，将超过10^4个特征点压缩为256个原型）来简化适应过程，使其更适合少量样本场景。
    *   **交叉注意力机制**：
        *   **Transformer 1**执行原型与输入特征之间的**交叉注意力**，生成中间特征。
        *   **Transformer 2**利用这些中间特征，再次与输入特征进行注意力交互，**输出经过适应的特征**。
        *   输出特征还通过一个**残差连接**与原始输入特征合并，即 `f̃ = Transformer2(C̃,f) + f`。
    *   **键投影（Key Projection）**：在Transformer 1中，**键投影矩阵（K）** 的作用至关重要。该方法假设，通过适当地更新K（从源域的Ks更新到目标域的Kt），Polyformer层能够将目标域的输入特征（遵循不同分布）投影到一个与源域相似的子空间。这使得转换后的特征在语义上与目标域的输出特征空间兼容，而无需更新原始模型权重。

*   **训练与适应流程**
    *   **源域训练**：
        *   在将Polyformer层插入到已在源域预训练的模型M中后，**模型M的所有权重（包括批量归一化BatchNorm参数）都被冻结**。
        *   **只有Polyformer层的权重被更新**，使其在新管线M'中保持与原始管线M相似的性能。
        *   此阶段训练完成后，原型嵌入就成为了源域特征的压缩表示。
    *   **目标域适应（少量样本场景）**：
        *   在目标域上，**模型M的所有权重（除了BatchNorm参数）再次被冻结**。
        *   **只有Polyformer中的K投影权重和BatchNorm参数被更新**。
        *   训练过程仅使用**少量（例如五张）已标注的目标域数据**进行监督。
        *   **域对抗学习集成（可选）**：为了进一步提升适应性能，可以融入传统的域对抗损失。总的目标函数可以表示为 `Ladapt(Xs,Xt, Yt) = Lsup(Xt, Yt) + Ladv(Xs,Xt)`。其中，对抗损失可以尝试区分源域和目标域的特征或预测的分割掩码。

*   **模型通用性**
    *   Polyformer可以与任何骨干网络（如U-Net、DeepLabV3+或基于Transformer的模型）结合使用。该论文以**U-Net**作为概念验证模型进行实验。


