# LaTeX 编译 PowerShell 脚本
# 用法: .\compile.ps1 [文档名] [-clean] [-open]

param(
    [string]$DocName = "main",
    [switch]$Clean,
    [switch]$Open
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "       LaTeX 编译脚本 (PowerShell)" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查文件是否存在
if (-not (Test-Path "$DocName.tex")) {
    Write-Host "错误: 找不到 $DocName.tex 文件！" -ForegroundColor Red
    exit 1
}

# 编译函数
function Invoke-LaTeXCompile {
    param([string]$Step, [string]$Command)
    
    Write-Host "[$Step] $Command..." -ForegroundColor Yellow
    $result = & cmd /c "$Command 2>&1"
    
    if ($LASTEXITCODE -ne 0) {
        Write-Host "错误: $Command 失败！" -ForegroundColor Red
        Write-Host "错误详情:" -ForegroundColor Red
        Write-Host $result -ForegroundColor Red
        return $false
    }
    return $true
}

# 开始编译
Write-Host "开始编译 $DocName.tex..." -ForegroundColor Green
Write-Host ""

# 第一次编译
if (-not (Invoke-LaTeXCompile "1/4" "pdflatex -interaction=nonstopmode $DocName.tex")) {
    exit 1
}

# BibTeX 处理
if (Test-Path "$DocName.bib") {
    if (-not (Invoke-LaTeXCompile "2/4" "bibtex $DocName")) {
        Write-Host "警告: BibTeX 处理可能有问题，继续编译..." -ForegroundColor Yellow
    }
} else {
    Write-Host "[2/4] 未找到 .bib 文件，跳过 bibtex..." -ForegroundColor Yellow
}

# 第二次编译
if (-not (Invoke-LaTeXCompile "3/4" "pdflatex -interaction=nonstopmode $DocName.tex")) {
    exit 1
}

# 第三次编译
if (-not (Invoke-LaTeXCompile "4/4" "pdflatex -interaction=nonstopmode $DocName.tex")) {
    exit 1
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "           编译完成！" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# 检查结果
if (Test-Path "$DocName.pdf") {
    $fileSize = (Get-Item "$DocName.pdf").Length
    Write-Host "✓ PDF 文件已生成: $DocName.pdf" -ForegroundColor Green
    Write-Host "✓ 文件大小: $fileSize 字节" -ForegroundColor Green
    
    # 自动打开 PDF
    if ($Open) {
        Start-Process "$DocName.pdf"
        Write-Host "✓ PDF 文件已打开" -ForegroundColor Green
    }
} else {
    Write-Host "✗ 错误: PDF 文件未生成！" -ForegroundColor Red
    exit 1
}

# 清理临时文件
if ($Clean) {
    Write-Host ""
    Write-Host "清理临时文件..." -ForegroundColor Yellow
    
    $tempFiles = @("$DocName.aux", "$DocName.bbl", "$DocName.blg", 
                   "$DocName.brf", "$DocName.out", "$DocName.synctex.gz")
    
    foreach ($file in $tempFiles) {
        if (Test-Path $file) {
            Remove-Item $file -Force
            Write-Host "✓ 已删除: $file" -ForegroundColor Green
        }
    }
}

Write-Host ""
Write-Host "编译日志保存在: $DocName.log" -ForegroundColor Cyan
Write-Host "如有问题，请查看日志文件。" -ForegroundColor Cyan
