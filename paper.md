

### Methodology (Focus on SAM 2's Memory Mechanism)

SAM 2 is developed as a unified foundation model for promptable visual segmentation in both images and videos, generalizing the Segment Anything (SA) task to the temporal domain. While sharing core principles with SAM, SAM 2's capacity for video processing is crucially enabled by its enhanced memory mechanism.

The SAM 2 architecture comprises:
Image Encoder: This component processes video frames in a streaming fashion, providing unconditioned feature embeddings for each frame. It uses an MAE pre-trained Hiera image encoder, which supports multiscale features for decoding.
Prompt Encoder: Identical to SAM's, it encodes prompts such as clicks (positive/negative), bounding boxes, or masks to define the object's extent in a frame.
Mask Decoder: Following SAM's design, it uses "two-way" Transformer blocks to update prompt and frame embeddings. It predicts multiple masks for ambiguous prompts to ensure valid outputs, a feature extended to video frames where ambiguity can persist over time. An occlusion prediction head is also included to forecast if the object of interest is visible in the current frame.

The memory mechanism is central to SAM 2's ability to track objects and maintain temporal coherence in videos:
Memory Attention: This module is critical for conditioning the current frame's features on information from past frames and previous predictions, as well as any new prompts. It consists of Transformer blocks that perform self-attention and then cross-attention to a "memory bank".
Memory Bank: This acts as a repository for historical object information. It stores spatial feature maps from past frames and information from previously prompted frames.
Memory Encoder: This component is responsible for generating the memory that populates the memory bank. It fuses the current output mask (derived from the mask decoder's prediction) with the unconditioned frame embedding from the image encoder. Specifically, the mask token from the mask decoder's output is used as an "object pointer token" and placed into the memory bank.
Object Pointers: These are lightweight vectors, derived from the mask decoder's output tokens, that provide high-level semantic information about the object being segmented. They are cross-attended to by the memory attention module, enhancing tracking performance.

This integrated memory architecture enables SAM 2 to effectively leverage temporal context, allowing for real-time video processing and coherent object tracking across frames, even in dynamic or occluded scenarios.